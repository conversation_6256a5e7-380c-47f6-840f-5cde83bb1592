{"accessCodeLifespan": 60, "accessCodeLifespanLogin": 1800, "accessCodeLifespanUserAction": 300, "accessTokenLifespan": 300, "accessTokenLifespanForImplicitFlow": 900, "actionTokenGeneratedByAdminLifespan": 43200, "actionTokenGeneratedByUserLifespan": 300, "adminEventsDetailsEnabled": false, "adminEventsEnabled": false, "adminPermissionsEnabled": false, "attributes": {"cibaAuthRequestedUserHint": "login_hint", "cibaBackchannelTokenDeliveryMode": "poll", "cibaExpiresIn": "120", "cibaInterval": "5", "oauth2DeviceCodeLifespan": "600", "oauth2DevicePollingInterval": "5", "parRequestUriLifespan": "60", "realmReusableOtpCode": "false"}, "authenticationFlows": [{"alias": "Account verification options", "authenticationExecutions": [{"authenticator": "idp-email-verification", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "ALTERNATIVE", "userSetupAllowed": false}, {"authenticatorFlow": true, "autheticatorFlow": true, "flowAlias": "Verify Existing Account by Re-authentication", "priority": 20, "requirement": "ALTERNATIVE", "userSetupAllowed": false}], "builtIn": true, "description": "Method with which to verity the existing account", "id": "5a7f6e41-fae7-4bb7-ac08-f13288f6b573", "providerId": "basic-flow", "topLevel": false}, {"alias": "Browser - Conditional 2FA", "authenticationExecutions": [{"authenticator": "auth-otp-form", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 20, "requirement": "ALTERNATIVE", "userSetupAllowed": false}, {"authenticator": "auth-recovery-authn-code-form", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 40, "requirement": "DISABLED", "userSetupAllowed": false}, {"authenticator": "conditional-user-configured", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "REQUIRED", "userSetupAllowed": false}, {"authenticator": "webauthn-authenticator", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 30, "requirement": "DISABLED", "userSetupAllowed": false}], "builtIn": true, "description": "Flow to determine if any 2FA is required for the authentication", "id": "5c4a79e5-b082-4cca-8b47-5c553cfbb779", "providerId": "basic-flow", "topLevel": false}, {"alias": "Browser - Conditional Organization", "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "REQUIRED", "userSetupAllowed": false}, {"authenticator": "organization", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 20, "requirement": "ALTERNATIVE", "userSetupAllowed": false}], "builtIn": true, "description": "Flow to determine if the organization identity-first login is to be used", "id": "03e2ba4b-c5b9-43d9-af71-c99848230ad9", "providerId": "basic-flow", "topLevel": false}, {"alias": "Direct Grant - Conditional OTP", "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "REQUIRED", "userSetupAllowed": false}, {"authenticator": "direct-grant-validate-otp", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 20, "requirement": "REQUIRED", "userSetupAllowed": false}], "builtIn": true, "description": "Flow to determine if the OTP is required for the authentication", "id": "0ea369f1-2c89-4488-9f06-5861e714be4b", "providerId": "basic-flow", "topLevel": false}, {"alias": "First Broker Login - Conditional Organization", "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "REQUIRED", "userSetupAllowed": false}, {"authenticator": "idp-add-organization-member", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 20, "requirement": "REQUIRED", "userSetupAllowed": false}], "builtIn": true, "description": "Flow to determine if the authenticator that adds organization members is to be used", "id": "62bfa544-e086-4bcb-af74-f5fbef5dd32a", "providerId": "basic-flow", "topLevel": false}, {"alias": "First broker login - Conditional 2FA", "authenticationExecutions": [{"authenticator": "auth-otp-form", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 20, "requirement": "ALTERNATIVE", "userSetupAllowed": false}, {"authenticator": "auth-recovery-authn-code-form", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 40, "requirement": "DISABLED", "userSetupAllowed": false}, {"authenticator": "conditional-user-configured", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "REQUIRED", "userSetupAllowed": false}, {"authenticator": "webauthn-authenticator", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 30, "requirement": "DISABLED", "userSetupAllowed": false}], "builtIn": true, "description": "Flow to determine if any 2FA is required for the authentication", "id": "37cc6443-fa25-4194-8624-00cb7c72880f", "providerId": "basic-flow", "topLevel": false}, {"alias": "<PERSON><PERSON> Existing Account", "authenticationExecutions": [{"authenticator": "idp-confirm-link", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "REQUIRED", "userSetupAllowed": false}, {"authenticatorFlow": true, "autheticatorFlow": true, "flowAlias": "Account verification options", "priority": 20, "requirement": "REQUIRED", "userSetupAllowed": false}], "builtIn": true, "description": "Handle what to do if there is existing account with same email/username like authenticated identity provider", "id": "9aa10806-933a-4d71-82a5-c2066097c446", "providerId": "basic-flow", "topLevel": false}, {"alias": "Reset - Conditional OTP", "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "REQUIRED", "userSetupAllowed": false}, {"authenticator": "reset-otp", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 20, "requirement": "REQUIRED", "userSetupAllowed": false}], "builtIn": true, "description": "Flow to determine if the OTP should be reset or not. Set to REQUIRED to force.", "id": "8b69bd7f-c07e-462c-b766-2efc7dbb08f0", "providerId": "basic-flow", "topLevel": false}, {"alias": "User creation or linking", "authenticationExecutions": [{"authenticator": "idp-create-user-if-unique", "authenticatorConfig": "create unique user config", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "ALTERNATIVE", "userSetupAllowed": false}, {"authenticatorFlow": true, "autheticatorFlow": true, "flowAlias": "<PERSON><PERSON> Existing Account", "priority": 20, "requirement": "ALTERNATIVE", "userSetupAllowed": false}], "builtIn": true, "description": "Flow for the existing/non-existing user alternatives", "id": "15cf6850-c84d-4df5-a804-f1b43584884b", "providerId": "basic-flow", "topLevel": false}, {"alias": "Verify Existing Account by Re-authentication", "authenticationExecutions": [{"authenticator": "idp-username-password-form", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "REQUIRED", "userSetupAllowed": false}, {"authenticatorFlow": true, "autheticatorFlow": true, "flowAlias": "First broker login - Conditional 2FA", "priority": 20, "requirement": "CONDITIONAL", "userSetupAllowed": false}], "builtIn": true, "description": "Reauthentication of existing account", "id": "20b52b37-d087-4e1e-b28a-367bc5f3afc5", "providerId": "basic-flow", "topLevel": false}, {"alias": "browser", "authenticationExecutions": [{"authenticator": "auth-cookie", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "ALTERNATIVE", "userSetupAllowed": false}, {"authenticator": "auth-spnego", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 20, "requirement": "DISABLED", "userSetupAllowed": false}, {"authenticator": "identity-provider-redirector", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 25, "requirement": "ALTERNATIVE", "userSetupAllowed": false}, {"authenticatorFlow": true, "autheticatorFlow": true, "flowAlias": "Organization", "priority": 26, "requirement": "ALTERNATIVE", "userSetupAllowed": false}, {"authenticatorFlow": true, "autheticatorFlow": true, "flowAlias": "forms", "priority": 30, "requirement": "ALTERNATIVE", "userSetupAllowed": false}], "builtIn": true, "description": "Browser based authentication", "id": "9e1b1a60-b093-46e1-b174-1cf23a130d0d", "providerId": "basic-flow", "topLevel": true}, {"alias": "clients", "authenticationExecutions": [{"authenticator": "client-jwt", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 20, "requirement": "ALTERNATIVE", "userSetupAllowed": false}, {"authenticator": "client-secret", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "ALTERNATIVE", "userSetupAllowed": false}, {"authenticator": "client-secret-jwt", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 30, "requirement": "ALTERNATIVE", "userSetupAllowed": false}, {"authenticator": "client-x509", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 40, "requirement": "ALTERNATIVE", "userSetupAllowed": false}], "builtIn": true, "description": "Base authentication for clients", "id": "543595db-d2a5-444f-8f71-06bc2d3f4ef4", "providerId": "client-flow", "topLevel": true}, {"alias": "direct grant", "authenticationExecutions": [{"authenticator": "direct-grant-validate-password", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 20, "requirement": "REQUIRED", "userSetupAllowed": false}, {"authenticator": "direct-grant-validate-username", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "REQUIRED", "userSetupAllowed": false}, {"authenticatorFlow": true, "autheticatorFlow": true, "flowAlias": "Direct Grant - Conditional OTP", "priority": 30, "requirement": "CONDITIONAL", "userSetupAllowed": false}], "builtIn": true, "description": "OpenID Connect Resource Owner Grant", "id": "81349153-d267-4c4b-9ac1-eb073d97454e", "providerId": "basic-flow", "topLevel": true}, {"alias": "docker auth", "authenticationExecutions": [{"authenticator": "docker-http-basic-authenticator", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "REQUIRED", "userSetupAllowed": false}], "builtIn": true, "description": "Used by Docker clients to authenticate against the IDP", "id": "e8b2dd0e-7173-4c82-b1f4-1262fbffac77", "providerId": "basic-flow", "topLevel": true}, {"alias": "first broker login", "authenticationExecutions": [{"authenticator": "idp-review-profile", "authenticatorConfig": "review profile config", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "REQUIRED", "userSetupAllowed": false}, {"authenticatorFlow": true, "autheticatorFlow": true, "flowAlias": "First Broker Login - Conditional Organization", "priority": 50, "requirement": "CONDITIONAL", "userSetupAllowed": false}, {"authenticatorFlow": true, "autheticatorFlow": true, "flowAlias": "User creation or linking", "priority": 20, "requirement": "REQUIRED", "userSetupAllowed": false}], "builtIn": true, "description": "Actions taken after first broker login with identity provider account, which is not yet linked to any Keycloak account", "id": "ddb2b0f2-1b09-4774-8155-1b2811596e9d", "providerId": "basic-flow", "topLevel": true}, {"alias": "forms", "authenticationExecutions": [{"authenticator": "auth-username-password-form", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "REQUIRED", "userSetupAllowed": false}, {"authenticatorFlow": true, "autheticatorFlow": true, "flowAlias": "Browser - Conditional 2FA", "priority": 20, "requirement": "CONDITIONAL", "userSetupAllowed": false}], "builtIn": true, "description": "Username, password, otp and other auth forms.", "id": "bc70b725-26ec-4adb-87e6-94e481ca05e3", "providerId": "basic-flow", "topLevel": false}, {"alias": "registration", "authenticationExecutions": [{"authenticator": "registration-page-form", "authenticatorFlow": true, "autheticatorFlow": true, "flowAlias": "registration form", "priority": 10, "requirement": "REQUIRED", "userSetupAllowed": false}], "builtIn": true, "description": "Registration flow", "id": "3344bf78-5603-4846-b1bc-9c8f95a36838", "providerId": "basic-flow", "topLevel": true}, {"alias": "registration form", "authenticationExecutions": [{"authenticator": "registration-password-action", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 50, "requirement": "REQUIRED", "userSetupAllowed": false}, {"authenticator": "registration-recaptcha-action", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 60, "requirement": "DISABLED", "userSetupAllowed": false}, {"authenticator": "registration-terms-and-conditions", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 70, "requirement": "DISABLED", "userSetupAllowed": false}, {"authenticator": "registration-user-creation", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 20, "requirement": "REQUIRED", "userSetupAllowed": false}], "builtIn": true, "description": "Registration form", "id": "8f5df331-cdb7-4d1a-bfb7-31a4e831cdd4", "providerId": "form-flow", "topLevel": false}, {"alias": "reset credentials", "authenticationExecutions": [{"authenticator": "reset-credential-email", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 20, "requirement": "REQUIRED", "userSetupAllowed": false}, {"authenticator": "reset-credentials-choose-user", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "REQUIRED", "userSetupAllowed": false}, {"authenticator": "reset-password", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 30, "requirement": "REQUIRED", "userSetupAllowed": false}, {"authenticatorFlow": true, "autheticatorFlow": true, "flowAlias": "Reset - Conditional OTP", "priority": 40, "requirement": "CONDITIONAL", "userSetupAllowed": false}], "builtIn": true, "description": "Reset credentials for a user if they forgot their password or something", "id": "24e0db94-3662-4e00-a918-0de3fd4a0d65", "providerId": "basic-flow", "topLevel": true}, {"alias": "saml ecp", "authenticationExecutions": [{"authenticator": "http-basic-authenticator", "authenticatorFlow": false, "autheticatorFlow": false, "priority": 10, "requirement": "REQUIRED", "userSetupAllowed": false}], "builtIn": true, "description": "SAML ECP Profile Authentication Flow", "id": "d283df1d-5ed5-4c40-ab26-c11f56bc9c7c", "providerId": "basic-flow", "topLevel": true}, {"alias": "Organization", "authenticationExecutions": [{"authenticatorFlow": true, "autheticatorFlow": true, "flowAlias": "Browser - Conditional Organization", "priority": 10, "requirement": "CONDITIONAL", "userSetupAllowed": false}], "builtIn": true, "id": "b39b0e85-35e5-4168-b4e8-387f082ca92d", "providerId": "basic-flow", "topLevel": false}], "authenticatorConfig": [{"alias": "create unique user config", "config": {"require.password.update.after.registration": "false"}, "id": "eb04a77d-1334-408c-9d3e-92ffa3638211"}, {"alias": "review profile config", "config": {"update.profile.on.first.login": "missing"}, "id": "8b9a65b2-b3e0-4b21-b349-552c90b6694f"}], "browserFlow": "browser", "browserSecurityHeaders": {"contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "contentSecurityPolicyReportOnly": "", "referrerPolicy": "no-referrer", "strictTransportSecurity": "max-age=********; includeSubDomains", "xContentTypeOptions": "nosniff", "xFrameOptions": "SAMEORIGIN", "xRobotsTag": "none"}, "bruteForceProtected": false, "bruteForceStrategy": "MULTIPLE", "clientAuthenticationFlow": "clients", "clientOfflineSessionIdleTimeout": 0, "clientOfflineSessionMaxLifespan": 0, "clientPolicies": {"policies": []}, "clientProfiles": {"profiles": []}, "clientScopeMappings": {"account": [{"client": "account-console", "roles": ["manage-account", "view-groups"]}]}, "clientScopes": [{"attributes": {"consent.screen.text": "${offlineAccessScopeConsentText}", "display.on.consent.screen": "true"}, "description": "OpenID Connect built-in scope: offline_access", "id": "28d48d9c-45dd-41d9-a1cb-561129a840cb", "name": "offline_access", "protocol": "openid-connect"}, {"attributes": {"consent.screen.text": "${samlRoleListScopeConsentText}", "display.on.consent.screen": "true"}, "description": "SAML role list", "id": "f34b95b8-d4fa-4dba-93c6-427f5c14cbbb", "name": "role_list", "protocol": "saml", "protocolMappers": [{"config": {"attribute.name": "Role", "attribute.nameformat": "Basic", "single": "false"}, "consentRequired": false, "id": "18fc7251-c0ec-463f-9172-d0564d0746f6", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper"}]}, {"attributes": {"consent.screen.text": "", "display.on.consent.screen": "false", "include.in.token.scope": "false"}, "description": "OpenID Connect scope for add allowed web origins to the access token", "id": "765450e7-bd6f-437e-b665-29e1f21dc4fb", "name": "web-origins", "protocol": "openid-connect", "protocolMappers": [{"config": {"access.token.claim": "true", "introspection.token.claim": "true"}, "consentRequired": false, "id": "ae054ac0-b99a-4cbe-a606-94779f792d53", "name": "allowed web origins", "protocol": "openid-connect", "protocolMapper": "oidc-allowed-origins-mapper"}]}, {"attributes": {"consent.screen.text": "${addressScopeConsentText}", "display.on.consent.screen": "true", "include.in.token.scope": "true"}, "description": "OpenID Connect built-in scope: address", "id": "5e0a2b4b-963d-43da-be4c-4ebd08d988b3", "name": "address", "protocol": "openid-connect", "protocolMappers": [{"config": {"access.token.claim": "true", "id.token.claim": "true", "introspection.token.claim": "true", "user.attribute.country": "country", "user.attribute.formatted": "formatted", "user.attribute.locality": "locality", "user.attribute.postal_code": "postal_code", "user.attribute.region": "region", "user.attribute.street": "street", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "29f31218-3957-4916-ad4a-afd8f65f4302", "name": "address", "protocol": "openid-connect", "protocolMapper": "oidc-address-mapper"}]}, {"attributes": {"consent.screen.text": "${emailScopeConsentText}", "display.on.consent.screen": "true", "include.in.token.scope": "true"}, "description": "OpenID Connect built-in scope: email", "id": "532bf40a-ffce-4bba-aa7c-0141d0d6db7c", "name": "email", "protocol": "openid-connect", "protocolMappers": [{"config": {"access.token.claim": "true", "claim.name": "email", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.attribute": "email", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "ffb3274f-3866-4ec3-ac47-82f8ba5cd658", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "email_verified", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "boolean", "user.attribute": "emailVerified", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "0c58c4f8-0068-4fff-a040-5d122bab951c", "name": "email verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper"}]}, {"attributes": {"consent.screen.text": "${organizationScopeConsentText}", "display.on.consent.screen": "true", "include.in.token.scope": "true"}, "description": "Additional claims about the organization a subject belongs to", "id": "82cbe478-4b14-4b67-80a7-26a1f1ed426c", "name": "organization", "protocol": "openid-connect", "protocolMappers": [{"config": {"access.token.claim": "true", "claim.name": "organization", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "multivalued": "true"}, "consentRequired": false, "id": "7727f514-cda9-492c-96ae-19b40b0ee9dd", "name": "organization", "protocol": "openid-connect", "protocolMapper": "oidc-organization-membership-mapper"}]}, {"attributes": {"consent.screen.text": "${phoneScopeConsentText}", "display.on.consent.screen": "true", "include.in.token.scope": "true"}, "description": "OpenID Connect built-in scope: phone", "id": "79bc8af5-1554-491c-9f2e-01f66f4cb7bd", "name": "phone", "protocol": "openid-connect", "protocolMappers": [{"config": {"access.token.claim": "true", "claim.name": "phone_number", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.attribute": "phoneNumber", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "a9aa28c1-9c9d-4cc4-9595-9fff6298b2b3", "name": "phone number", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "phone_number_verified", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "boolean", "user.attribute": "phoneNumberVerified", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "49411c9c-c863-4b3f-91b6-0388548b9a24", "name": "phone number verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}]}, {"attributes": {"consent.screen.text": "${profileScopeConsentText}", "display.on.consent.screen": "true", "include.in.token.scope": "true"}, "description": "OpenID Connect built-in scope: profile", "id": "240f792a-6c99-400a-8e5e-2b3f2ae84ee7", "name": "profile", "protocol": "openid-connect", "protocolMappers": [{"config": {"access.token.claim": "true", "claim.name": "birthdate", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.attribute": "birthdate", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "37dd3c17-534b-446d-96c4-ffb9215bb17c", "name": "birthdate", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "family_name", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.attribute": "lastName", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "eae91364-e48c-405e-a5ee-b2638d7b13c1", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "gender", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.attribute": "gender", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "bc44600f-6460-4681-b91f-b27ae047f6e4", "name": "gender", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "given_name", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.attribute": "firstName", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "4e297abd-676f-4e62-865b-c13ea14c6e56", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "locale", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.attribute": "locale", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "c1282f27-2aa7-4703-9de7-fc3b7ca66451", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "middle_name", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.attribute": "middleName", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "e68efc6f-a4b0-4a00-9f5b-56a50e215d07", "name": "middle name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "nickname", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.attribute": "nickname", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "d92e5659-7421-4072-b091-77dcaa0b82a8", "name": "nickname", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "picture", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.attribute": "picture", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "16e757a5-3d62-4413-885b-afc65ba4f582", "name": "picture", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "preferred_username", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.attribute": "username", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "bf0b2c95-7afd-456e-9fd1-9e3e89fe48f7", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "profile", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.attribute": "profile", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "e452afbd-0fdd-4f1d-a30b-00fdfb71c7f3", "name": "profile", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "updated_at", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "long", "user.attribute": "updatedAt", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "d48ab432-e70f-459c-8f7f-f24397cda4ff", "name": "updated at", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "website", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.attribute": "website", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "f2e50ea7-54ee-4631-8353-9222ca156abc", "name": "website", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "zoneinfo", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.attribute": "zoneinfo", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "3a455a43-42f4-4876-b04f-ca029863432f", "name": "zoneinfo", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}, {"config": {"access.token.claim": "true", "id.token.claim": "true", "introspection.token.claim": "true", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "711414d5-55ae-4d22-b388-3c3653c4533c", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper"}]}, {"attributes": {"consent.screen.text": "${rolesScopeConsentText}", "display.on.consent.screen": "true", "include.in.token.scope": "false"}, "description": "OpenID Connect scope for add user roles to the access token", "id": "3e958ed3-6805-4b58-958e-d650863907d1", "name": "roles", "protocol": "openid-connect", "protocolMappers": [{"config": {"access.token.claim": "true", "claim.name": "realm_access.roles", "introspection.token.claim": "true", "jsonType.label": "String", "multivalued": "true", "user.attribute": "foo"}, "consentRequired": false, "id": "24b2c4f9-d55d-4eac-bc95-1cb1e1ab154a", "name": "realm roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "resource_access.${client_id}.roles", "introspection.token.claim": "true", "jsonType.label": "String", "multivalued": "true", "user.attribute": "foo"}, "consentRequired": false, "id": "9a780bdb-a224-420e-8246-240173f6267b", "name": "client roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-client-role-mapper"}, {"config": {"access.token.claim": "true", "introspection.token.claim": "true"}, "consentRequired": false, "id": "3b466e2e-06bf-4d53-b5d6-83d65ff76eb5", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper"}]}, {"attributes": {"display.on.consent.screen": "false"}, "description": "Organization Membership", "id": "81c81538-82ed-42c4-b2ee-a326ebfb00dd", "name": "saml_organization", "protocol": "saml", "protocolMappers": [{"config": {}, "consentRequired": false, "id": "514abd09-ae16-4003-b0c1-9de66f56988c", "name": "organization", "protocol": "saml", "protocolMapper": "saml-organization-membership-mapper"}]}, {"attributes": {"display.on.consent.screen": "false", "include.in.token.scope": "false"}, "description": "OpenID Connect scope for add acr (authentication context class reference) to the token", "id": "71e478f3-228a-4863-a826-88c3fd523e07", "name": "acr", "protocol": "openid-connect", "protocolMappers": [{"config": {"access.token.claim": "true", "id.token.claim": "true", "introspection.token.claim": "true"}, "consentRequired": false, "id": "68eaeaac-196c-490f-998b-9d067dafd077", "name": "acr loa level", "protocol": "openid-connect", "protocolMapper": "oidc-acr-mapper"}]}, {"attributes": {"display.on.consent.screen": "false", "include.in.token.scope": "false"}, "description": "OpenID Connect scope for add all basic claims to the token", "id": "4a2e3c6c-4166-4f8e-91bd-ed6a6ace768c", "name": "basic", "protocol": "openid-connect", "protocolMappers": [{"config": {"access.token.claim": "true", "claim.name": "auth_time", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "long", "user.session.note": "AUTH_TIME"}, "consentRequired": false, "id": "462dfa36-1358-448f-a704-d047003cb09c", "name": "auth_time", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper"}, {"config": {"access.token.claim": "true", "introspection.token.claim": "true"}, "consentRequired": false, "id": "fe46cfe6-18d2-4827-9511-2911ef7eff62", "name": "sub", "protocol": "openid-connect", "protocolMapper": "oidc-sub-mapper"}]}, {"attributes": {"display.on.consent.screen": "false", "include.in.token.scope": "false"}, "description": "Specific scope for a client enabled for service accounts", "id": "f701aac2-6563-48bc-940e-736175c0f5ca", "name": "service_account", "protocol": "openid-connect", "protocolMappers": [{"config": {"access.token.claim": "true", "claim.name": "clientAddress", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.session.note": "clientAddress"}, "consentRequired": false, "id": "40469c83-6f35-4146-9173-1a886c262284", "name": "Client IP Address", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "clientHost", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.session.note": "clientHost"}, "consentRequired": false, "id": "385b2476-adae-4282-a1ff-2ae3a59fff68", "name": "Client Host", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "client_id", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.session.note": "client_id"}, "consentRequired": false, "id": "c307aea9-c5f6-4c9b-95d9-85a322825056", "name": "Client ID", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper"}]}, {"attributes": {"display.on.consent.screen": "false", "include.in.token.scope": "true"}, "description": "Microprofile - JWT built-in scope", "id": "f87aaaf0-3a0b-4f60-99b6-f05a802e1716", "name": "microprofile-jwt", "protocol": "openid-connect", "protocolMappers": [{"config": {"access.token.claim": "true", "claim.name": "groups", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "multivalued": "true", "user.attribute": "foo"}, "consentRequired": false, "id": "154b4447-b705-4073-80f4-f2df96f6e996", "name": "groups", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper"}, {"config": {"access.token.claim": "true", "claim.name": "upn", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.attribute": "username", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "81e809fa-7720-4e08-9151-75065fb5116c", "name": "upn", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}]}], "clientSessionIdleTimeout": 0, "clientSessionMaxLifespan": 0, "clients": [{"alwaysDisplayInConsole": false, "attributes": {"client.use.lightweight.access.token.enabled": "true", "pkce.code.challenge.method": "S256", "post.logout.redirect.uris": "+", "realm_client": "false"}, "authenticationFlowBindingOverrides": {}, "baseUrl": "/admin/onramp-dev/console/", "bearerOnly": false, "clientAuthenticatorType": "client-secret", "clientId": "security-admin-console", "consentRequired": false, "defaultClientScopes": ["acr", "basic", "email", "profile", "roles", "web-origins"], "directAccessGrantsEnabled": false, "enabled": true, "frontchannelLogout": false, "fullScopeAllowed": true, "id": "e079f6c2-e279-4604-bc5d-7fc6f92b1bb0", "implicitFlowEnabled": false, "name": "${client_security-admin-console}", "nodeReRegistrationTimeout": 0, "notBefore": 0, "optionalClientScopes": ["address", "microprofile-jwt", "offline_access", "organization", "phone"], "protocol": "openid-connect", "protocolMappers": [{"config": {"access.token.claim": "true", "claim.name": "locale", "id.token.claim": "true", "introspection.token.claim": "true", "jsonType.label": "String", "user.attribute": "locale", "userinfo.token.claim": "true"}, "consentRequired": false, "id": "7f37ba9c-4ee1-4319-b076-0d653e64d28d", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper"}], "publicClient": true, "redirectUris": ["/admin/onramp-dev/console/*"], "rootUrl": "${authAdminUrl}", "serviceAccountsEnabled": false, "standardFlowEnabled": true, "surrogateAuthRequired": false, "webOrigins": ["+"]}, {"alwaysDisplayInConsole": false, "attributes": {"pkce.code.challenge.method": "S256", "post.logout.redirect.uris": "+", "realm_client": "false"}, "authenticationFlowBindingOverrides": {}, "baseUrl": "/realms/onramp-dev/account/", "bearerOnly": false, "clientAuthenticatorType": "client-secret", "clientId": "account-console", "consentRequired": false, "defaultClientScopes": ["acr", "basic", "email", "profile", "roles", "web-origins"], "directAccessGrantsEnabled": false, "enabled": true, "frontchannelLogout": false, "fullScopeAllowed": false, "id": "208615e9-89b8-41e0-9331-7dd3eae51048", "implicitFlowEnabled": false, "name": "${client_account-console}", "nodeReRegistrationTimeout": 0, "notBefore": 0, "optionalClientScopes": ["address", "microprofile-jwt", "offline_access", "organization", "phone"], "protocol": "openid-connect", "protocolMappers": [{"config": {}, "consentRequired": false, "id": "cc76acb3-769a-40c0-a164-97146384c0c9", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper"}], "publicClient": true, "redirectUris": ["/realms/onramp-dev/account/*"], "rootUrl": "${authBaseUrl}", "serviceAccountsEnabled": false, "standardFlowEnabled": true, "surrogateAuthRequired": false, "webOrigins": []}, {"alwaysDisplayInConsole": false, "attributes": {"post.logout.redirect.uris": "+", "realm_client": "false"}, "authenticationFlowBindingOverrides": {}, "baseUrl": "/realms/onramp-dev/account/", "bearerOnly": false, "clientAuthenticatorType": "client-secret", "clientId": "account", "consentRequired": false, "defaultClientScopes": ["acr", "basic", "email", "profile", "roles", "web-origins"], "directAccessGrantsEnabled": false, "enabled": true, "frontchannelLogout": false, "fullScopeAllowed": false, "id": "0bc67303-ff25-481f-a96f-0dee769bc880", "implicitFlowEnabled": false, "name": "${client_account}", "nodeReRegistrationTimeout": 0, "notBefore": 0, "optionalClientScopes": ["address", "microprofile-jwt", "offline_access", "organization", "phone"], "protocol": "openid-connect", "publicClient": true, "redirectUris": ["/realms/onramp-dev/account/*"], "rootUrl": "${authBaseUrl}", "serviceAccountsEnabled": false, "standardFlowEnabled": true, "surrogateAuthRequired": false, "webOrigins": []}, {"alwaysDisplayInConsole": false, "attributes": {"client.use.lightweight.access.token.enabled": "true", "realm_client": "false"}, "authenticationFlowBindingOverrides": {}, "bearerOnly": false, "clientAuthenticatorType": "client-secret", "clientId": "admin-cli", "consentRequired": false, "defaultClientScopes": ["acr", "basic", "email", "profile", "roles", "web-origins"], "directAccessGrantsEnabled": true, "enabled": true, "frontchannelLogout": false, "fullScopeAllowed": true, "id": "b4d8e4ad-6f19-4be8-9f02-2723248b8460", "implicitFlowEnabled": false, "name": "${client_admin-cli}", "nodeReRegistrationTimeout": 0, "notBefore": 0, "optionalClientScopes": ["address", "microprofile-jwt", "offline_access", "organization", "phone"], "protocol": "openid-connect", "publicClient": true, "redirectUris": [], "serviceAccountsEnabled": false, "standardFlowEnabled": false, "surrogateAuthRequired": false, "webOrigins": []}, {"alwaysDisplayInConsole": false, "attributes": {"realm_client": "true"}, "authenticationFlowBindingOverrides": {}, "bearerOnly": true, "clientAuthenticatorType": "client-secret", "clientId": "broker", "consentRequired": false, "defaultClientScopes": ["acr", "basic", "email", "profile", "roles", "web-origins"], "directAccessGrantsEnabled": false, "enabled": true, "frontchannelLogout": false, "fullScopeAllowed": false, "id": "25d78b82-bf29-4d78-8e21-11886d69fb5a", "implicitFlowEnabled": false, "name": "${client_broker}", "nodeReRegistrationTimeout": 0, "notBefore": 0, "optionalClientScopes": ["address", "microprofile-jwt", "offline_access", "organization", "phone"], "protocol": "openid-connect", "publicClient": false, "redirectUris": [], "serviceAccountsEnabled": false, "standardFlowEnabled": true, "surrogateAuthRequired": false, "webOrigins": []}, {"alwaysDisplayInConsole": false, "attributes": {"realm_client": "true"}, "authenticationFlowBindingOverrides": {}, "bearerOnly": true, "clientAuthenticatorType": "client-secret", "clientId": "realm-management", "consentRequired": false, "defaultClientScopes": ["acr", "basic", "email", "profile", "roles", "web-origins"], "directAccessGrantsEnabled": false, "enabled": true, "frontchannelLogout": false, "fullScopeAllowed": false, "id": "c894710c-da55-415c-bc13-cfa007cfb360", "implicitFlowEnabled": false, "name": "${client_realm-management}", "nodeReRegistrationTimeout": 0, "notBefore": 0, "optionalClientScopes": ["address", "microprofile-jwt", "offline_access", "organization", "phone"], "protocol": "openid-connect", "publicClient": false, "redirectUris": [], "serviceAccountsEnabled": false, "standardFlowEnabled": true, "surrogateAuthRequired": false, "webOrigins": []}], "components": {"org.keycloak.keys.KeyProvider": [{"config": {"algorithm": ["RSA-OAEP"], "certificate": ["MIICozCCAYsCBgGX7RemYDANBgkqhkiG9w0BAQsFADAVMRMwEQYDVQQDDApvbnJhbXAtZGV2MB4XDTI1MDcwOTAyNDg1OVoXDTM1MDcwOTAyNTAzOVowFTETMBEGA1UEAwwKb25yYW1wLWRldjCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAJVnH5H9s+mz8hT3IqHaq6AgtMibvLRm4eFbKh4cxUMJSKu0rS8vMh3RmX5xVRRS6VsywmgDO0Pxs/EU2zjvTmi5Jygkf1Cr/nwEu2MtmN48+wbtYs4PideyPcNes2Nm658UKBzOExV0cbESjWuJ64X8aJDx4yrAAj7NfoKgceQ5Gupiw6qA2gG6TBtnpwCBd1z8YPhTbEzDD7nScxsREHTOF7O/YUX5QRo+DZhuVlaskSs/zY4YiGFUuDP3KQ4fc8r0PC2s5JWglMwSzHdM4bxcmHtCSRBDe3UtyBOC0TIJV2EgSvDjg53n1+jBK4jeLraKZo4mgEY2rztMAEH9tZUCAwEAATANBgkqhkiG9w0BAQsFAAOCAQEAE/vpBsDVxAajTZ19sUXl2Q/POqO4D2Vsu/lVK0YRr6/1g8RI9Yn9YioPyexDPsZrKhfIspnCHG8dO6fXIW0kLwcXAcIZS6n8wBeK55C992Q0U7Mw5jDV55RWAj7Mc6eAWdql8I9lu5o0tem3JTbDKRjuS6N22H7atZx6WDYih56NuDXFfhmfanoIVVjabbiVxt3H6bSyfLlqRPskIwzFxZ1x3xCCRyZf3VIki8DlEAMNXdQZjPlFC4ccl4zqLZ/mxQZjt1MOxlIZsZK8IWWTana3dMNeNBG90Fv9SzspEWpuNw7Z6cVmrv43i+ZW1oBEaWlxsboio7xYG77DJ7NLJg=="], "keyUse": ["ENC"], "priority": ["100"], "privateKey": ["MIIEoQIBAAKCAQEAlWcfkf2z6bPyFPciodqroCC0yJu8tGbh4VsqHhzFQwlIq7StLy8yHdGZfnFVFFLpWzLCaAM7Q/Gz8RTbOO9OaLknKCR/UKv+fAS7Yy2Y3jz7Bu1izg+J17I9w16zY2brnxQoHM4TFXRxsRKNa4nrhfxokPHjKsACPs1+gqBx5Dka6mLDqoDaAbpMG2enAIF3XPxg+FNsTMMPudJzGxEQdM4Xs79hRflBGj4NmG5WVqyRKz/NjhiIYVS4M/cpDh9zyvQ8LazklaCUzBLMd0zhvFyYe0JJEEN7dS3IE4LRMglXYSBK8OODnefX6MEriN4utopmjiaARjavO0wAQf21lQIDAQABAoH/AQACa+BnVq6VnQXQG6ctIg6dN+r/24g9jLXF7f2HDXSSkHQ0wLy0Kgv/uAsvtcuJX92e0jSmQ+9mKo78OZrfEczFOfF61GMiYrRpx85ADJHEteJvOtLq90nBHo8vOus+5OT7bPJyBWhnjSiKxHbVKS/lNaZSAjbuAbDciVnR5KXznDrhSkEcs8cVPwfXX+ijM9+8j0QhXSsUmYCJontLc8TP/ydFuiwwbEpTfWclam69I7I6CysLcNL7teXEC9P5LNno0UzGchiBDty9Dkd2/hpNC9wOeyQUCkOOFPgPXM18BvS473ul23NNrMVFuLacbB/COEVjisFsCKAUa4FhAoGBAMYsMbiWORoB8wnEhch0DUj4Z2z8aKGQqtemnsBxu+mPOTkSkA8fnWe94arRJoo6VOTgOYSdakpTxjZ7CBpaOdvdRWT6RfRQLIdwni/0D4XiRtzuOO0izP7TY6vNLoSdc02XGuWT7QJUpWDc6vE3uBtE95sSiG+6UDfRefcjw/CJAoGBAMD/v4UfJiucA8UQdP9uIYX9hJIKLDXSaGiq2JEyFC7bDazS/q8yWmwX1kIsUSogDaV1YSBMtjisCIr51Jvr/LuTErkGW2BPaaIi8QZVOCKryqLpkztmr3D8g9x5KHGGdDBjD0S5BTiLaystMs1odj5lVDb7ZaJ6xVhqmUG6XqGtAoGBAJhsD4YhDSRi0XL208tc74brgGlELpnt8mlRv5LXJ6g5v9KU43auY1wSoQrIOXNh8D/l8nd8t+8ZRkBLGu1h7lSceS50jO+f5s/5n8HUX0gjxUtSczh5ZuCs6GGQvDHGBJRuH4Lq5wW+40CASrhjmdhnkGaWt5fv3plAJiYfPSq5AoGAOKmdaA8KHNmUPcIkJ24FB5qz1lCIRFD++GPYa2CBjnQforLbtUIaTAv/irQKLEz21LIOjvYWtbLEW1EX+6MgM03tvEbCgxctkz2g99QDRCKdvpDG+jSeo5RvfkZP+myYJjCfJfUQNFayYx3FAYt+07jaLb5HS5mx4NCDKEn4SI0CgYADP3UMMB604+bBithh2/6bpDyRfxH1CnPRUDNgnl54X/wvORIvrkoDfdG9h7Fa7PWoziGaiH7kmj4ymc1JXGB9hAHhYi+Pt5D+qq2Pnmv8POgISvMPBk3zHbemE9pUqwKWnFap/MxY/NuWImpQFO8p8IBoB0bAx3grF3Sfto+Qdw=="]}, "id": "fdb1743d-8ba6-4f46-863f-66c91425e1b1", "name": "rsa-enc-generated", "providerId": "rsa-enc-generated", "subComponents": {}}, {"config": {"algorithm": ["HS512"], "kid": ["179f46c3-bf0f-4db4-920b-02f93d0dc03a"], "priority": ["100"], "secret": ["NqBHk69f-y9XVqjXAgFf5dl-tNnD9rXYkirbNGvMnmXa9IXXU8XhhnPSZHmJOoTHPvOd8OWPRb86hV9DXLNR-YTxT5feF1DVKHq-dkFxKJIW7OLFi8nHZXug4U88AX7yZI-dvWHtglJLaJTgy0FLSD-7gCteFrlOs7RZb5kddWY"]}, "id": "4fc95573-8f39-41ba-9e76-205d4516bd57", "name": "hmac-generated-hs512", "providerId": "hmac-generated", "subComponents": {}}, {"config": {"certificate": ["MIICozCCAYsCBgGX7RemBTANBgkqhkiG9w0BAQsFADAVMRMwEQYDVQQDDApvbnJhbXAtZGV2MB4XDTI1MDcwOTAyNDg1OVoXDTM1MDcwOTAyNTAzOVowFTETMBEGA1UEAwwKb25yYW1wLWRldjCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAOptVgK+8Y+jDCpykuLuIHwS5Q1VuJ7XIheZucrgS7X7CIWjqPGvoOZyJGGQCfJrd9HwK24ZEk/DP/S8i9OU55iUcCTMoDXPzK6eZrNCGtPtzQ+HUJOHBCpp7YaHxNP8w7X1i63LNTQWz/9RpGmyVCXaWRxsoyOy0CWZmM7BrjM1d4yAD7cYO+dJ6Jgd8sPAG+Z5KFS1eEejWB4uLMfNsD6PzHR418oBxhnOpS2fvrQHAofpP3QuvwWvg+Y5vFecq6hd20Mf6AidMHItfg1jn/yD9b6uBM0tWkHwNATtww6aX4xbmFKZsL18dRkL1p2yYmGoVJh6OALQNNY3qQ7RfOkCAwEAATANBgkqhkiG9w0BAQsFAAOCAQEAAyqJSL5T/E1w1bG5gvSUpfNMfsQdPykrNcH88JHns32K/iTmyduouQyGIZT5Q4q1WPVOn8eGkR97MOlvnpvRM0pMiQ4yBYIJn8dKpZ8fhxW4c29hhsQ/RInwdDNSgVaZamWPms8PacXhha8yg+9MNlFjhgjlPDRt6qwTv+HKGxs4nTmnZS7dga63x1/L5816arqcC7v+sZe7PsSE4YDu2FhBR+f0qjmKCMCFOSaYD3rUsqWR2vmke4GVfiHcfJZs3s9K6I4KnTtOJY06SYrgXXkdSlpl7ix6XJ/a5fVXvanv8wGiZXnt8VXhkra1Rw5GzNlhL2jaPh96yNsbI0gJog=="], "keyUse": ["SIG"], "priority": ["100"], "privateKey": ["MIIEpAIBAAKCAQEA6m1WAr7xj6MMKnKS4u4gfBLlDVW4ntciF5m5yuBLtfsIhaOo8a+g5nIkYZAJ8mt30fArbhkST8M/9LyL05TnmJRwJMygNc/Mrp5ms0Ia0+3ND4dQk4cEKmnthofE0/zDtfWLrcs1NBbP/1GkabJUJdpZHGyjI7LQJZmYzsGuMzV3jIAPtxg750nomB3yw8Ab5nkoVLV4R6NYHi4sx82wPo/MdHjXygHGGc6lLZ++tAcCh+k/dC6/Ba+D5jm8V5yrqF3bQx/oCJ0wci1+DWOf/IP1vq4EzS1aQfA0BO3DDppfjFuYUpmwvXx1GQvWnbJiYahUmHo4AtA01jepDtF86QIDAQABAoIBAHRIlOQazB8oTSYXHDZ10WZKRxU0wrYD1/Tl+6ufU97qrWEA/BIawNDeqJ4+gTifR1m7bQeZZyRqoSRojGGBGNpuMyyF1iirMYgiqaXcsgsTB+deg0EStaXGUpyrZx6SYJA3QziLtpHmiPlIiaomW6hFJXO8jbXtAmPMS2iv2RWryiYY+NqFWUJNoklhDOL4TmHjzKB3uOy4UXCmqGpHM9uQ6cnxCRpbgJRUSobCoHYA6KKov9mWvxN22gghvhURyimR60QM/nxff4akSCzIffkEb0bARSMsCekjOiuaTEyxCjtV7p37s9orVLXUjfmOx+e2y7oSvI2NLxLadJkrzJMCgYEA/6JKVOTmbGfZg+LQCE922xhEaSzcVu0H5mEmv/jsKCaFLxcGjHaIlcPmtmuord1CZev3wuXpmhKHUtV085r2Nq/xzT+/EkvJdp2ggVi6fjSrWszsCOiydXw8M9Kkpy+FL4EFZ0+88BKmuqiYbFasqlcB1GEsyBj0OQ2nBwXFBUMCgYEA6sNFi/xBrhxy66ba8thBincl4v75kVS9D5ISI2Us/tAwZ0QPLUrMZCRFkN+GsJJXJWgks122gnNLHN7cDXtla6fRevdS4F2jx9SnlP1C/C6aHKleIa40gejsIF3Ye7x0Tghh38pelTT7bx9vpAPaB4rV+ZScrlDLMX5hLOtcfGMCgYBi5FyPFPITE+WwwKir3lnjjjLA4UroEdBRjlnYQCrTqXsyaVzuL9jM3zk8bs+VWxgZp7x0WE8TfIF/SPZyWwtOwqfvN+7p3Am26mmyQCn9MtDrUoUpOSP4kTQU9DMgPiDu7KGzgsRbt0tqwFge6TTovLoHeARD8GtiOubXr6K1UwKBgQCLlOrm3mdW1Edn4V9RAE+HumvmWV+dj1qRxXHPZbqpDrOY0piLf2ujJwXNq3lzxq7V6OPZWaDIUzCXy9Hemj2lErS0fhTingphKAkzxplrQ3ISFImJUQCbNRgf7mxmtZgZRVacLsCpykSsv24rgLbDwUKUEE5QPm1x0giJIq9srQKBgQDQyZJZRi8SLuP10uRRcR3tSFrImdTotmIwF9ZYvjgM+cGdrBspe8hCZCC0x3gBR/Q+3McqpqQ7t6uzFM/+vlLtHk43k7k9Mi3e/gCw8e7nj0bGbQKyC0dFxhCp+LkfeVwroQC+DoSQIdzsi7Vhe7u3L3hm889MHXOa5cVwnYxmkA=="]}, "id": "fe917bf2-99e6-4139-bbc3-0e1d670d7761", "name": "rsa-generated", "providerId": "rsa-generated", "subComponents": {}}, {"config": {"kid": ["88995f75-0efc-410c-9175-cb87c07bbd00"], "priority": ["100"], "secret": ["bfBJJduB13uapY3_L7Hb3A"]}, "id": "388411a6-2ded-4a04-b2bc-d66fede30df0", "name": "aes-generated", "providerId": "aes-generated", "subComponents": {}}], "org.keycloak.services.clientregistration.policy.ClientRegistrationPolicy": [{"config": {}, "id": "4dc5d452-9fcc-4248-a6be-4083e9e12055", "name": "Consent Required", "providerId": "consent-required", "subComponents": {}, "subType": "anonymous"}, {"config": {}, "id": "517272c1-7f58-40b6-8613-51f095ffd85e", "name": "Full Scope Disabled", "providerId": "scope", "subComponents": {}, "subType": "anonymous"}, {"config": {"allow-default-scopes": ["true"]}, "id": "5698d9ad-f47d-4a15-9a0b-6939d49b17cb", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subComponents": {}, "subType": "authenticated"}, {"config": {"allow-default-scopes": ["true"]}, "id": "b753239b-cc83-498d-8ae9-58dba6b70e61", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subComponents": {}, "subType": "anonymous"}, {"config": {"allowed-protocol-mapper-types": ["oidc-address-mapper", "oidc-full-name-mapper", "oidc-sha256-pairwise-sub-mapper", "oidc-usermodel-attribute-mapper", "oidc-usermodel-property-mapper", "saml-role-list-mapper", "saml-user-attribute-mapper", "saml-user-property-mapper"]}, "id": "d0c2e26a-96a6-49da-9ac2-f2b8e18dc0de", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subComponents": {}, "subType": "authenticated"}, {"config": {"allowed-protocol-mapper-types": ["oidc-address-mapper", "oidc-full-name-mapper", "oidc-sha256-pairwise-sub-mapper", "oidc-usermodel-attribute-mapper", "oidc-usermodel-property-mapper", "saml-role-list-mapper", "saml-user-attribute-mapper", "saml-user-property-mapper"]}, "id": "daa7004b-67d9-4d23-8d77-95c0844458ee", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subComponents": {}, "subType": "anonymous"}, {"config": {"client-uris-must-match": ["true"], "host-sending-registration-request-must-match": ["true"]}, "id": "a7cbb973-a264-4063-8ff9-0ae8fc5939c7", "name": "Trusted Hosts", "providerId": "trusted-hosts", "subComponents": {}, "subType": "anonymous"}, {"config": {"max-clients": ["200"]}, "id": "e03c03f9-faf0-4c61-9a39-d28308ddc0cc", "name": "Max Clients Limit", "providerId": "max-clients", "subComponents": {}, "subType": "anonymous"}]}, "defaultDefaultClientScopes": ["acr", "basic", "email", "profile", "role_list", "roles", "saml_organization", "web-origins"], "defaultOptionalClientScopes": ["address", "microprofile-jwt", "offline_access", "organization", "phone"], "defaultRole": {"clientRole": false, "composite": true, "containerId": "49ed04f1-f90f-4024-9dd3-9decad3e07f4", "description": "${role_default-roles}", "id": "cb0ef407-1b4e-4789-bfea-022fcbbd9b80", "name": "default-roles-onramp-dev"}, "defaultSignatureAlgorithm": "RS256", "directGrantFlow": "direct grant", "dockerAuthenticationFlow": "docker auth", "duplicateEmailsAllowed": false, "editUsernameAllowed": false, "enabled": true, "enabledEventTypes": [], "eventsEnabled": false, "eventsListeners": ["jboss-logging"], "failureFactor": 30, "firstBrokerLoginFlow": "first broker login", "groups": [], "id": "49ed04f1-f90f-4024-9dd3-9decad3e07f4", "identityProviderMappers": [], "identityProviders": [], "internationalizationEnabled": false, "keycloakVersion": "26.3.1", "localizationTexts": {}, "loginWithEmailAllowed": true, "maxDeltaTimeSeconds": 43200, "maxFailureWaitSeconds": 900, "maxTemporaryLockouts": 0, "minimumQuickLoginWaitSeconds": 60, "notBefore": 0, "oauth2DeviceCodeLifespan": 600, "oauth2DevicePollingInterval": 5, "offlineSessionIdleTimeout": 2592000, "offlineSessionMaxLifespan": 5184000, "offlineSessionMaxLifespanEnabled": false, "organizationsEnabled": false, "otpPolicyAlgorithm": "HmacSHA1", "otpPolicyCodeReusable": false, "otpPolicyDigits": 6, "otpPolicyInitialCounter": 0, "otpPolicyLookAheadWindow": 1, "otpPolicyPeriod": 30, "otpPolicyType": "totp", "otpSupportedApplications": ["totpAppFreeOTPName", "totpAppGoogleName", "totpAppMicrosoftAuthenticatorName"], "permanentLockout": false, "quickLoginCheckMilliSeconds": 1000, "realm": "onramp-dev", "refreshTokenMaxReuse": 0, "registrationAllowed": false, "registrationEmailAsUsername": false, "registrationFlow": "registration", "rememberMe": false, "requiredActions": [{"alias": "CONFIGURE_RECOVERY_AUTHN_CODES", "config": {}, "defaultAction": false, "enabled": true, "name": "Recovery Authentication Codes", "priority": 120, "providerId": "CONFIGURE_RECOVERY_AUTHN_CODES"}, {"alias": "CONFIGURE_TOTP", "config": {}, "defaultAction": false, "enabled": true, "name": "Configure OTP", "priority": 10, "providerId": "CONFIGURE_TOTP"}, {"alias": "TERMS_AND_CONDITIONS", "config": {}, "defaultAction": false, "enabled": false, "name": "Terms and Conditions", "priority": 20, "providerId": "TERMS_AND_CONDITIONS"}, {"alias": "UPDATE_PASSWORD", "config": {}, "defaultAction": false, "enabled": true, "name": "Update Password", "priority": 30, "providerId": "UPDATE_PASSWORD"}, {"alias": "UPDATE_PROFILE", "config": {}, "defaultAction": false, "enabled": true, "name": "Update Profile", "priority": 40, "providerId": "UPDATE_PROFILE"}, {"alias": "VERIFY_EMAIL", "config": {}, "defaultAction": false, "enabled": true, "name": "<PERSON><PERSON><PERSON>", "priority": 50, "providerId": "VERIFY_EMAIL"}, {"alias": "VERIFY_PROFILE", "config": {}, "defaultAction": false, "enabled": true, "name": "Verify Profile", "priority": 90, "providerId": "VERIFY_PROFILE"}, {"alias": "delete_account", "config": {}, "defaultAction": false, "enabled": false, "name": "Delete Account", "priority": 60, "providerId": "delete_account"}, {"alias": "delete_credential", "config": {}, "defaultAction": false, "enabled": true, "name": "Delete Credential", "priority": 100, "providerId": "delete_credential"}, {"alias": "idp_link", "config": {}, "defaultAction": false, "enabled": true, "name": "Linking Identity Provider", "priority": 110, "providerId": "idp_link"}, {"alias": "update_user_locale", "config": {}, "defaultAction": false, "enabled": true, "name": "Update User Locale", "priority": 1000, "providerId": "update_user_locale"}, {"alias": "webauthn-register", "config": {}, "defaultAction": false, "enabled": true, "name": "Webauthn Register", "priority": 70, "providerId": "webauthn-register"}, {"alias": "webauthn-register-passwordless", "config": {}, "defaultAction": false, "enabled": true, "name": "Webauthn Register Passwordless", "priority": 80, "providerId": "webauthn-register-passwordless"}], "requiredCredentials": ["password"], "resetCredentialsFlow": "reset credentials", "resetPasswordAllowed": false, "revokeRefreshToken": false, "roles": {"client": {"account": [{"attributes": {}, "clientRole": true, "composite": true, "composites": {"client": {"account": ["manage-account-links"]}}, "containerId": "0bc67303-ff25-481f-a96f-0dee769bc880", "description": "${role_manage-account}", "id": "83bf044e-2adb-43e2-876e-607fcbe4fcf4", "name": "manage-account"}, {"attributes": {}, "clientRole": true, "composite": true, "composites": {"client": {"account": ["view-consent"]}}, "containerId": "0bc67303-ff25-481f-a96f-0dee769bc880", "description": "${role_manage-consent}", "id": "ea734e7a-589f-4ce3-93b9-dfbd0c067bc6", "name": "manage-consent"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "0bc67303-ff25-481f-a96f-0dee769bc880", "description": "${role_delete-account}", "id": "898a9f7a-d6f8-4cce-ac84-a539186f6836", "name": "delete-account"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "0bc67303-ff25-481f-a96f-0dee769bc880", "description": "${role_manage-account-links}", "id": "30c9b3ec-cbbb-4cf0-8e1b-ec82b6ab8e35", "name": "manage-account-links"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "0bc67303-ff25-481f-a96f-0dee769bc880", "description": "${role_view-applications}", "id": "865cad78-a068-4501-b063-7b139e97da03", "name": "view-applications"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "0bc67303-ff25-481f-a96f-0dee769bc880", "description": "${role_view-consent}", "id": "6857f920-d989-47ac-95d9-e6e8cf11d557", "name": "view-consent"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "0bc67303-ff25-481f-a96f-0dee769bc880", "description": "${role_view-groups}", "id": "967d0a8b-a9b4-4715-bd83-08e6a6a3a52a", "name": "view-groups"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "0bc67303-ff25-481f-a96f-0dee769bc880", "description": "${role_view-profile}", "id": "86f5b18a-0283-479a-8d1a-19e44095a4f0", "name": "view-profile"}], "account-console": [], "admin-cli": [], "broker": [{"attributes": {}, "clientRole": true, "composite": false, "containerId": "25d78b82-bf29-4d78-8e21-11886d69fb5a", "description": "${role_read-token}", "id": "77425c2a-1c64-4de5-8880-03fc2f2a3fc2", "name": "read-token"}], "realm-management": [{"attributes": {}, "clientRole": true, "composite": true, "composites": {"client": {"realm-management": ["create-client", "impersonation", "manage-authorization", "manage-clients", "manage-events", "manage-identity-providers", "manage-realm", "manage-users", "query-clients", "query-groups", "query-realms", "query-users", "view-authorization", "view-clients", "view-events", "view-identity-providers", "view-realm", "view-users"]}}, "containerId": "c894710c-da55-415c-bc13-cfa007cfb360", "description": "${role_realm-admin}", "id": "10aef12d-59cd-4274-80a4-3284b972845c", "name": "realm-admin"}, {"attributes": {}, "clientRole": true, "composite": true, "composites": {"client": {"realm-management": ["query-clients"]}}, "containerId": "c894710c-da55-415c-bc13-cfa007cfb360", "description": "${role_view-clients}", "id": "d66ed148-ffab-4153-a551-cf9233dcdec2", "name": "view-clients"}, {"attributes": {}, "clientRole": true, "composite": true, "composites": {"client": {"realm-management": ["query-groups", "query-users"]}}, "containerId": "c894710c-da55-415c-bc13-cfa007cfb360", "description": "${role_view-users}", "id": "60258fb1-6cd6-463a-80e6-fa2b232fd884", "name": "view-users"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "c894710c-da55-415c-bc13-cfa007cfb360", "description": "${role_create-client}", "id": "aa36f09a-bffa-4a4a-ae2b-a73fcf332d91", "name": "create-client"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "c894710c-da55-415c-bc13-cfa007cfb360", "description": "${role_impersonation}", "id": "677613ae-b902-4af1-bd09-39acabf1c227", "name": "impersonation"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "c894710c-da55-415c-bc13-cfa007cfb360", "description": "${role_manage-authorization}", "id": "9c8b5079-f150-4b22-9e01-85cd3349f965", "name": "manage-authorization"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "c894710c-da55-415c-bc13-cfa007cfb360", "description": "${role_manage-clients}", "id": "f9f394b0-a6c5-40d9-99c8-715ef5f32b8e", "name": "manage-clients"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "c894710c-da55-415c-bc13-cfa007cfb360", "description": "${role_manage-events}", "id": "1b4629ce-ed23-4243-a359-4c5d5ca8f537", "name": "manage-events"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "c894710c-da55-415c-bc13-cfa007cfb360", "description": "${role_manage-identity-providers}", "id": "16152df4-83f4-433a-beb8-a33ce24f25e8", "name": "manage-identity-providers"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "c894710c-da55-415c-bc13-cfa007cfb360", "description": "${role_manage-realm}", "id": "1999ebf6-9522-4383-96a3-aa3ff36b66c1", "name": "manage-realm"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "c894710c-da55-415c-bc13-cfa007cfb360", "description": "${role_manage-users}", "id": "2f9563e1-4099-449c-9586-0817c5c197da", "name": "manage-users"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "c894710c-da55-415c-bc13-cfa007cfb360", "description": "${role_query-clients}", "id": "791c6f46-4625-421d-953d-a36c97ffc389", "name": "query-clients"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "c894710c-da55-415c-bc13-cfa007cfb360", "description": "${role_query-groups}", "id": "aced9e59-fc98-4215-af35-e109c7984d48", "name": "query-groups"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "c894710c-da55-415c-bc13-cfa007cfb360", "description": "${role_query-realms}", "id": "dcbad49b-286c-4762-aeb9-b9d9b1510683", "name": "query-realms"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "c894710c-da55-415c-bc13-cfa007cfb360", "description": "${role_query-users}", "id": "fa5c22f5-2491-4972-bb5e-fabb656abe19", "name": "query-users"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "c894710c-da55-415c-bc13-cfa007cfb360", "description": "${role_view-authorization}", "id": "45db2054-75e4-42cb-b09a-dbddf18dbfca", "name": "view-authorization"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "c894710c-da55-415c-bc13-cfa007cfb360", "description": "${role_view-events}", "id": "42bf6755-b181-464f-a3e4-bcfaad8d3592", "name": "view-events"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "c894710c-da55-415c-bc13-cfa007cfb360", "description": "${role_view-identity-providers}", "id": "7d1bc766-b34f-4f86-b186-476f18d8775a", "name": "view-identity-providers"}, {"attributes": {}, "clientRole": true, "composite": false, "containerId": "c894710c-da55-415c-bc13-cfa007cfb360", "description": "${role_view-realm}", "id": "26b94753-7266-41d0-bf0b-0eb91f40db43", "name": "view-realm"}], "security-admin-console": []}, "realm": [{"attributes": {}, "clientRole": false, "composite": true, "composites": {"client": {"account": ["manage-account", "view-profile"]}, "realm": ["offline_access", "uma_authorization"]}, "containerId": "49ed04f1-f90f-4024-9dd3-9decad3e07f4", "description": "${role_default-roles}", "id": "cb0ef407-1b4e-4789-bfea-022fcbbd9b80", "name": "default-roles-onramp-dev"}, {"attributes": {}, "clientRole": false, "composite": false, "containerId": "49ed04f1-f90f-4024-9dd3-9decad3e07f4", "description": "${role_offline-access}", "id": "ec8ba8f7-2043-4e38-a90b-7570536905c1", "name": "offline_access"}, {"attributes": {}, "clientRole": false, "composite": false, "containerId": "49ed04f1-f90f-4024-9dd3-9decad3e07f4", "description": "${role_uma_authorization}", "id": "b1777ac8-8961-4bcb-9bb6-f18941635841", "name": "uma_authorization"}]}, "scopeMappings": [{"clientScope": "offline_access", "roles": ["offline_access"]}], "smtpServer": {}, "sslRequired": "external", "ssoSessionIdleTimeout": 1800, "ssoSessionIdleTimeoutRememberMe": 0, "ssoSessionMaxLifespan": 36000, "ssoSessionMaxLifespanRememberMe": 0, "userManagedAccessAllowed": false, "verifiableCredentialsEnabled": false, "verifyEmail": false, "waitIncrementSeconds": 60, "webAuthnPolicyAcceptableAaguids": [], "webAuthnPolicyAttestationConveyancePreference": "not specified", "webAuthnPolicyAuthenticatorAttachment": "not specified", "webAuthnPolicyAvoidSameAuthenticatorRegister": false, "webAuthnPolicyCreateTimeout": 0, "webAuthnPolicyExtraOrigins": [], "webAuthnPolicyPasswordlessAcceptableAaguids": [], "webAuthnPolicyPasswordlessAttestationConveyancePreference": "not specified", "webAuthnPolicyPasswordlessAuthenticatorAttachment": "not specified", "webAuthnPolicyPasswordlessAvoidSameAuthenticatorRegister": false, "webAuthnPolicyPasswordlessCreateTimeout": 0, "webAuthnPolicyPasswordlessExtraOrigins": [], "webAuthnPolicyPasswordlessRequireResidentKey": "not specified", "webAuthnPolicyPasswordlessRpEntityName": "keycloak", "webAuthnPolicyPasswordlessRpId": "", "webAuthnPolicyPasswordlessSignatureAlgorithms": ["ES256", "RS256"], "webAuthnPolicyPasswordlessUserVerificationRequirement": "not specified", "webAuthnPolicyRequireResidentKey": "not specified", "webAuthnPolicyRpEntityName": "keycloak", "webAuthnPolicyRpId": "", "webAuthnPolicySignatureAlgorithms": ["ES256", "RS256"], "webAuthnPolicyUserVerificationRequirement": "not specified"}