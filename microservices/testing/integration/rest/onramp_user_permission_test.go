package rest

import (
	"io"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/api/authorizer"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
	"synapse-its.com/testing/utils"
)

func Test_OnrampUserPermission_Success(t *testing.T) {
	assert := assert.New(t)
	ctx := t.Context()

	// Set up database connections
	connections := connect.NewConnections(ctx)
	pg := connections.Postgres

	// Get user permissions
	userPermissions, err := authorizer.GetUserPermissions(pg, "45627c04-8d87-595a-a31b-2e675e22417a")
	logger.Debugf("userPermissions UserID: %+v", userPermissions.UserID)
	logger.Debugf("userPermissions: %+v", userPermissions)
	assert.NoError(err)
	assert.NotNil(userPermissions)
	assert.Equal(userPermissions.UserID, "45627c04-8d87-595a-a31b-2e675e22417a")

	// Add context to the user permissions
	ctx = authorizer.AddUserPermissionsToContext(ctx, userPermissions)
	logger.Debugf("ctx: %+v", ctx)

	// Get user permissions from the context
	userPermissions, ok := authorizer.UserPermissionsFromContext(ctx)
	logger.Debugf("userPermissions UserID: %+v", userPermissions.UserID)
	logger.Debugf("userPermissions: %+v", userPermissions)
	assert.True(ok)
	assert.NotNil(userPermissions)
	assert.Equal(userPermissions.UserID, "45627c04-8d87-595a-a31b-2e675e22417a")

	// Wait for onramp service to be ready
	assert.NoError(utils.AwaitOnramp(ctx, 30*time.Second), "onramp service should be ready")

	// The URL under test - FIXED: use the service name and correct port
	url := "http://onramp:8080/api/user/permissions"

	// Create a request with the context
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, url, nil)
	assert.NoError(err)
	logger.Debugf("context: %+v", ctx)
	logger.Debugf("Request: %+v", req)

	// Set headers exactly as in your curl
	req.Header.Set("Content-Type", "application/json")

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	assert.NoError(err)

	// Close the response body
	if err != nil {
		t.Fatalf("Unexpected error: %v", err)
		return
	}
	defer resp.Body.Close()

	// Verify the response
	assert.Equal(http.StatusOK, resp.StatusCode)

	// Wait 5 seconds for the message to be processed.
	time.Sleep(5 * time.Second)

	// Parse the response
	body, err := io.ReadAll(resp.Body)
	assert.NoError(err)

	// Print the response
	logger.Debugf("Response: %s", string(body))
}
