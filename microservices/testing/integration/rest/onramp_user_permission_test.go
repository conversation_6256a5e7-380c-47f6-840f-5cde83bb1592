package rest

import (
	"io"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/api/authorizer"
	"synapse-its.com/shared/api/jwttokens"
	"synapse-its.com/shared/api/security"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
	"synapse-its.com/testing/utils"
)

func Test_OnrampUserPermission_Success(t *testing.T) {
	assert := assert.New(t)
	ctx := t.Context()

	// Set up database connections
	connections := connect.NewConnections(ctx)
	pg := connections.Postgres

	// Get user permissions
	userPermissions, err := authorizer.GetUserPermissions(pg, "45627c04-8d87-595a-a31b-2e675e22417a")
	logger.Debugf("userPermissions UserID: %+v", userPermissions.UserID)
	logger.Debugf("userPermissions: %+v", userPermissions)
	assert.NoError(err)
	assert.NotNil(userPermissions)
	assert.Equal(userPermissions.UserID, "45627c04-8d87-595a-a31b-2e675e22417a")

	// Add context to the user permissions
	ctx = authorizer.AddUserPermissionsToContext(ctx, *userPermissions)
	logger.Debugf("ctx: %+v", ctx)

	// Get user permissions from the context
	userPermissions, ok := authorizer.UserPermissionsFromContext(ctx)
	logger.Debugf("userPermissions UserID: %+v", userPermissions.UserID)
	logger.Debugf("userPermissions: %+v", userPermissions)
	assert.True(ok)
	assert.NotNil(userPermissions)
	assert.Equal(userPermissions.UserID, "45627c04-8d87-595a-a31b-2e675e22417a")

	// Wait for onramp service to be ready
	assert.NoError(utils.AwaitOnramp(ctx, 30*time.Second), "onramp service should be ready")

	// Create a JWT token for the user
	jwtUserPermissions := jwttokens.UserPermissions{
		SoftwareGateway: []jwttokens.UserSoftwareGatewayAccess{},
		Device:          []jwttokens.UserDeviceAccess{},
	}
	jwtToken, expiresAt, err := jwttokens.CreateJwtTokenUsingDuration(userPermissions.UserID, time.Hour, jwtUserPermissions)
	assert.NoError(err)
	assert.NotEmpty(jwtToken)
	logger.Debugf("Created JWT token for user: %s", userPermissions.UserID)

	// Store the JWT token in the database so the authorization middleware can validate it
	query := `
		INSERT INTO {{UserToken}} (UserId, JWTToken, JWTTokenSha256, Created, Expiration)
		VALUES ($1, $2, $3, $4, $5)`
	_, err = pg.Exec(query, userPermissions.UserID, jwtToken, security.CalculateSHA256(jwtToken), time.Now().UTC(), expiresAt)
	assert.NoError(err)
	logger.Debugf("Stored JWT token in database for user: %s", userPermissions.UserID)

	// The URL under test - API backend runs on port 8080 in Docker environment
	// In local development, http://localhost:4200/api/user/permissions would be proxied to this
	url := "http://onramp:8080/api/user/permissions"

	// Create a request with the context
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, url, nil)
	assert.NoError(err)
	logger.Debugf("context: %+v", ctx)
	logger.Debugf("Request: %+v", req)

	// Set headers for JWT authentication
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("jwt-token", jwtToken)

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	assert.NoError(err)

	// Close the response body
	if err != nil {
		t.Fatalf("Unexpected error: %v", err)
		return
	}
	defer resp.Body.Close()

	// Parse the response
	body, err := io.ReadAll(resp.Body)
	assert.NoError(err)

	// Log the response for debugging
	logger.Debugf("Response Status: %s", resp.Status)
	logger.Debugf("Response Body: %s", string(body))

	// Assert the response - expect 200 OK with JWT authentication
	assert.Equal(http.StatusOK, resp.StatusCode, "Expected HTTP 200 OK with JWT authentication")
	assert.NotEmpty(body, "Response body should not be empty")
}
