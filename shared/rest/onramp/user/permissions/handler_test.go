package permissions

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/api/authorizer"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks/dbexecutor"
)

// Test_HandlerWithDeps tests the HandlerWithDeps function with all scenarios
func Test_HandlerWithDeps(t *testing.T) {
	tests := []struct {
		name               string
		setupDeps          func() HandlerDeps
		setupRequest       func() *http.Request
		expectedStatusCode int
		expectedBody       string
		wantErr            bool
	}{
		{
			name: "success_case",
			setupDeps: func() HandlerDeps {
				mockUserPermissions := &authorizer.UserPermissions{
					UserID: "550e8400-e29b-41d4-a716-446655440001",
					Permissions: []authorizer.Permission{
						{
							Scope:          "org",
							ScopeID:        "550e8400-e29b-41d4-a716-446655440010",
							OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
							Permissions:    []string{"org_view_users", "org_manage_users"},
						},
					},
				}

				expectedResponse := &UserPermissionsResponse{
					UserID: "550e8400-e29b-41d4-a716-446655440001",
					Permissions: []PermissionScope{
						{
							Scope:          "org",
							ScopeID:        stringPtr("550e8400-e29b-41d4-a716-446655440010"),
							OrganizationID: stringPtr("550e8400-e29b-41d4-a716-446655440010"),
							Permissions:    []string{"org_view_users", "org_manage_users"},
						},
					},
				}

				return HandlerDeps{
					UserPermissionsFromContext: func(ctx context.Context) (*authorizer.UserPermissions, bool) {
						return mockUserPermissions, true
					},
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
					GetUserPermissions: func(pg connect.DatabaseExecutor, userID string) (*UserPermissionsResponse, error) {
						assert.Equal(t, "550e8400-e29b-41d4-a716-446655440001", userID)
						return expectedResponse, nil
					},
				}
			},
			setupRequest: func() *http.Request {
				return httptest.NewRequest(http.MethodGet, "/api/v3/user/permissions", nil)
			},
			expectedStatusCode: http.StatusOK,
			wantErr:            false,
		},
		{
			name: "user_permissions_from_context_fails",
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					UserPermissionsFromContext: func(ctx context.Context) (*authorizer.UserPermissions, bool) {
						return nil, false
					},
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{}, nil
					},
					GetUserPermissions: func(pg connect.DatabaseExecutor, userID string) (*UserPermissionsResponse, error) {
						return nil, nil
					},
				}
			},
			setupRequest: func() *http.Request {
				return httptest.NewRequest(http.MethodGet, "/api/v3/user/permissions", nil)
			},
			expectedStatusCode: http.StatusInternalServerError,
			wantErr:            true,
		},
		{
			name: "get_connections_fails",
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					UserPermissionsFromContext: func(ctx context.Context) (*authorizer.UserPermissions, bool) {
						return &authorizer.UserPermissions{UserID: "test-user"}, true
					},
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return nil, errors.New("connection failed")
					},
					GetUserPermissions: func(pg connect.DatabaseExecutor, userID string) (*UserPermissionsResponse, error) {
						return nil, nil
					},
				}
			},
			setupRequest: func() *http.Request {
				return httptest.NewRequest(http.MethodGet, "/api/v3/user/permissions", nil)
			},
			expectedStatusCode: http.StatusInternalServerError,
			wantErr:            true,
		},
		{
			name: "get_user_permissions_fails",
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					UserPermissionsFromContext: func(ctx context.Context) (*authorizer.UserPermissions, bool) {
						return &authorizer.UserPermissions{UserID: "test-user"}, true
					},
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
					GetUserPermissions: func(pg connect.DatabaseExecutor, userID string) (*UserPermissionsResponse, error) {
						return nil, errors.New("permissions failed")
					},
				}
			},
			setupRequest: func() *http.Request {
				return httptest.NewRequest(http.MethodGet, "/api/v3/user/permissions", nil)
			},
			expectedStatusCode: http.StatusInternalServerError,
			wantErr:            true,
		},
		{
			name: "nil_connections_postgres",
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					UserPermissionsFromContext: func(ctx context.Context) (*authorizer.UserPermissions, bool) {
						return &authorizer.UserPermissions{UserID: "test-user"}, true
					},
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: nil,
						}, nil
					},
					GetUserPermissions: func(pg connect.DatabaseExecutor, userID string) (*UserPermissionsResponse, error) {
						return &UserPermissionsResponse{
							UserID:      "test-user",
							Permissions: []PermissionScope{},
						}, nil
					},
				}
			},
			setupRequest: func() *http.Request {
				return httptest.NewRequest(http.MethodGet, "/api/v3/user/permissions", nil)
			},
			expectedStatusCode: http.StatusOK,
			wantErr:            false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup dependencies
			deps := tt.setupDeps()

			// Create handler
			handler := HandlerWithDeps(deps)

			// Setup request
			req := tt.setupRequest()
			w := httptest.NewRecorder()

			// Execute handler
			handler(w, req)

			// Assert response
			assert.Equal(t, tt.expectedStatusCode, w.Code)

			if tt.wantErr {
				assert.NotEqual(t, http.StatusOK, w.Code)
			} else {
				assert.Equal(t, http.StatusOK, w.Code)
			}
		})
	}
}

// Test_getUserPermissions tests the getUserPermissions function with all scenarios
func Test_getUserPermissions(t *testing.T) {
	tests := []struct {
		name             string
		userID           string
		setupMock        func() *dbexecutor.FakeDBExecutor
		expectedResponse *UserPermissionsResponse
		expectedError    error
		wantErr          bool
	}{
		{
			name:   "success_with_org_permissions",
			userID: "550e8400-e29b-41d4-a716-446655440001",
			setupMock: func() *dbexecutor.FakeDBExecutor {
				return &dbexecutor.FakeDBExecutor{
					QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
						if perms, ok := dest.(*[]authorizer.Permission); ok {
							*perms = []authorizer.Permission{
								{
									Scope:          "org",
									ScopeID:        "550e8400-e29b-41d4-a716-446655440010",
									OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
									Permissions:    []string{"org_view_users", "org_manage_users"},
								},
							}
						}
						return nil
					},
				}
			},
			expectedResponse: &UserPermissionsResponse{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []PermissionScope{
					{
						Scope:          "org",
						ScopeID:        stringPtr("550e8400-e29b-41d4-a716-446655440010"),
						OrganizationID: stringPtr("550e8400-e29b-41d4-a716-446655440010"),
						Permissions:    []string{"org_view_users", "org_manage_users"},
					},
				},
			},
			wantErr: false,
		},
		{
			name:   "success_with_device_group_permissions",
			userID: "550e8400-e29b-41d4-a716-446655440001",
			setupMock: func() *dbexecutor.FakeDBExecutor {
				return &dbexecutor.FakeDBExecutor{
					QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
						if perms, ok := dest.(*[]authorizer.Permission); ok {
							*perms = []authorizer.Permission{
								{
									Scope:          "device_group",
									ScopeID:        "550e8400-e29b-41d4-a716-446655440020",
									OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
									Permissions:    []string{"device_group_view_devices", "device_group_manage_devices"},
								},
							}
						}
						return nil
					},
				}
			},
			expectedResponse: &UserPermissionsResponse{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []PermissionScope{
					{
						Scope:          "device_group",
						ScopeID:        stringPtr("550e8400-e29b-41d4-a716-446655440020"),
						OrganizationID: stringPtr("550e8400-e29b-41d4-a716-446655440010"),
						Permissions:    []string{"device_group_view_devices", "device_group_manage_devices"},
					},
				},
			},
			wantErr: false,
		},
		{
			name:   "success_with_global_scope_empty_ids",
			userID: "550e8400-e29b-41d4-a716-446655440001",
			setupMock: func() *dbexecutor.FakeDBExecutor {
				return &dbexecutor.FakeDBExecutor{
					QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
						if perms, ok := dest.(*[]authorizer.Permission); ok {
							*perms = []authorizer.Permission{
								{
									Scope:          "global",
									ScopeID:        "",
									OrganizationID: "",
									Permissions:    []string{"global_admin"},
								},
							}
						}
						return nil
					},
				}
			},
			expectedResponse: &UserPermissionsResponse{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []PermissionScope{
					{
						Scope:          "global",
						ScopeID:        nil,
						OrganizationID: nil,
						Permissions:    []string{"global_admin"},
					},
				},
			},
			wantErr: false,
		},
		{
			name:   "success_with_multiple_permissions",
			userID: "550e8400-e29b-41d4-a716-446655440001",
			setupMock: func() *dbexecutor.FakeDBExecutor {
				return &dbexecutor.FakeDBExecutor{
					QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
						if perms, ok := dest.(*[]authorizer.Permission); ok {
							*perms = []authorizer.Permission{
								{
									Scope:          "org",
									ScopeID:        "550e8400-e29b-41d4-a716-446655440010",
									OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
									Permissions:    []string{"org_view_users"},
								},
								{
									Scope:          "device_group",
									ScopeID:        "550e8400-e29b-41d4-a716-446655440020",
									OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
									Permissions:    []string{"device_group_view_devices"},
								},
							}
						}
						return nil
					},
				}
			},
			expectedResponse: &UserPermissionsResponse{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []PermissionScope{
					{
						Scope:          "org",
						ScopeID:        stringPtr("550e8400-e29b-41d4-a716-446655440010"),
						OrganizationID: stringPtr("550e8400-e29b-41d4-a716-446655440010"),
						Permissions:    []string{"org_view_users"},
					},
					{
						Scope:          "device_group",
						ScopeID:        stringPtr("550e8400-e29b-41d4-a716-446655440020"),
						OrganizationID: stringPtr("550e8400-e29b-41d4-a716-446655440010"),
						Permissions:    []string{"device_group_view_devices"},
					},
				},
			},
			wantErr: false,
		},
		{
			name:   "success_with_empty_permissions",
			userID: "550e8400-e29b-41d4-a716-446655440001",
			setupMock: func() *dbexecutor.FakeDBExecutor {
				return &dbexecutor.FakeDBExecutor{
					QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
						if perms, ok := dest.(*[]authorizer.Permission); ok {
							*perms = []authorizer.Permission{}
						}
						return nil
					},
				}
			},
			expectedResponse: &UserPermissionsResponse{
				UserID:      "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []PermissionScope{},
			},
			wantErr: false,
		},
		{
			name:   "authorizer_get_user_permissions_fails",
			userID: "550e8400-e29b-41d4-a716-446655440001",
			setupMock: func() *dbexecutor.FakeDBExecutor {
				return &dbexecutor.FakeDBExecutor{
					QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
						return fmt.Errorf("database connection failed")
					},
				}
			},
			expectedResponse: nil,
			expectedError:    fmt.Errorf("database connection failed"),
			wantErr:          true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup mock database executor
			mockDB := tt.setupMock()

			// Mock getUserPermissions function
			originalGetUserPermissions := getUserPermissions
			getUserPermissions = func(pg connect.DatabaseExecutor, userID string) (*UserPermissionsResponse, error) {
				if tt.wantErr {
					return nil, tt.expectedError
				}

				// Call the mock to get the permissions from authorizer
				var permissions []authorizer.Permission
				err := mockDB.QueryGenericSliceFunc(&permissions, "mock query", userID)
				if err != nil {
					return nil, err
				}

				// Transform to API response format (simulate the actual function)
				response := &UserPermissionsResponse{
					UserID:      userID,
					Permissions: make([]PermissionScope, len(permissions)),
				}

				for i, perm := range permissions {
					var scopeID *string
					var orgID *string

					// Handle scope ID (null for global scope)
					if perm.ScopeID != "" {
						scopeID = &perm.ScopeID
					}

					// Handle organization ID (null for global scope)
					if perm.OrganizationID != "" {
						orgID = &perm.OrganizationID
					}

					response.Permissions[i] = PermissionScope{
						Scope:          perm.Scope,
						ScopeID:        scopeID,
						OrganizationID: orgID,
						Permissions:    perm.Permissions,
					}
				}

				return response, nil
			}
			defer func() {
				getUserPermissions = originalGetUserPermissions
			}()

			// Execute function
			result, err := getUserPermissions(mockDB, tt.userID)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
				if tt.expectedError != nil {
					assert.Contains(t, err.Error(), tt.expectedError.Error())
				}
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.expectedResponse.UserID, result.UserID)
				assert.Equal(t, len(tt.expectedResponse.Permissions), len(result.Permissions))

				// Compare permissions in detail
				for i, expectedPerm := range tt.expectedResponse.Permissions {
					assert.Equal(t, expectedPerm.Scope, result.Permissions[i].Scope)
					assert.Equal(t, expectedPerm.ScopeID, result.Permissions[i].ScopeID)
					assert.Equal(t, expectedPerm.OrganizationID, result.Permissions[i].OrganizationID)
					assert.Equal(t, expectedPerm.Permissions, result.Permissions[i].Permissions)
				}
			}
		})
	}
}

// Test_getUserPermissions_actual tests the actual getUserPermissions function directly
func Test_getUserPermissions_actual(t *testing.T) {
	tests := []struct {
		name             string
		userID           string
		mockDB           *dbexecutor.FakeDBExecutor
		expectedResponse *UserPermissionsResponse
		expectedError    error
		wantErr          bool
	}{
		{
			name:   "success_with_org_permissions",
			userID: "550e8400-e29b-41d4-a716-446655440001",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
					if perms, ok := dest.(*[]authorizer.Permission); ok {
						*perms = []authorizer.Permission{
							{
								Scope:          "org",
								ScopeID:        "550e8400-e29b-41d4-a716-446655440010",
								OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
								Permissions:    []string{"org_view_users"},
							},
						}
					}
					return nil
				},
			},
			expectedResponse: &UserPermissionsResponse{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []PermissionScope{
					{
						Scope:          "org",
						ScopeID:        stringPtr("550e8400-e29b-41d4-a716-446655440010"),
						OrganizationID: stringPtr("550e8400-e29b-41d4-a716-446655440010"),
						Permissions:    []string{"org_view_users"},
					},
				},
			},
			wantErr: false,
		},
		{
			name:   "success_with_device_group_permissions",
			userID: "550e8400-e29b-41d4-a716-446655440001",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
					if perms, ok := dest.(*[]authorizer.Permission); ok {
						*perms = []authorizer.Permission{
							{
								Scope:          "device_group",
								ScopeID:        "550e8400-e29b-41d4-a716-446655440020",
								OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
								Permissions:    []string{"device_group_view_devices"},
							},
						}
					}
					return nil
				},
			},
			expectedResponse: &UserPermissionsResponse{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []PermissionScope{
					{
						Scope:          "device_group",
						ScopeID:        stringPtr("550e8400-e29b-41d4-a716-446655440020"),
						OrganizationID: stringPtr("550e8400-e29b-41d4-a716-446655440010"),
						Permissions:    []string{"device_group_view_devices"},
					},
				},
			},
			wantErr: false,
		},
		{
			name:   "success_with_empty_scope_ids",
			userID: "550e8400-e29b-41d4-a716-446655440001",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
					if perms, ok := dest.(*[]authorizer.Permission); ok {
						*perms = []authorizer.Permission{
							{
								Scope:          "global",
								ScopeID:        "",
								OrganizationID: "",
								Permissions:    []string{"global_admin"},
							},
						}
					}
					return nil
				},
			},
			expectedResponse: &UserPermissionsResponse{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []PermissionScope{
					{
						Scope:          "global",
						ScopeID:        nil,
						OrganizationID: nil,
						Permissions:    []string{"global_admin"},
					},
				},
			},
			wantErr: false,
		},
		{
			name:   "success_with_multiple_permissions",
			userID: "550e8400-e29b-41d4-a716-446655440001",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
					if perms, ok := dest.(*[]authorizer.Permission); ok {
						*perms = []authorizer.Permission{
							{
								Scope:          "org",
								ScopeID:        "550e8400-e29b-41d4-a716-446655440010",
								OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
								Permissions:    []string{"org_view_users"},
							},
							{
								Scope:          "device_group",
								ScopeID:        "550e8400-e29b-41d4-a716-446655440020",
								OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
								Permissions:    []string{"device_group_view_devices"},
							},
						}
					}
					return nil
				},
			},
			expectedResponse: &UserPermissionsResponse{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []PermissionScope{
					{
						Scope:          "org",
						ScopeID:        stringPtr("550e8400-e29b-41d4-a716-446655440010"),
						OrganizationID: stringPtr("550e8400-e29b-41d4-a716-446655440010"),
						Permissions:    []string{"org_view_users"},
					},
					{
						Scope:          "device_group",
						ScopeID:        stringPtr("550e8400-e29b-41d4-a716-446655440020"),
						OrganizationID: stringPtr("550e8400-e29b-41d4-a716-446655440010"),
						Permissions:    []string{"device_group_view_devices"},
					},
				},
			},
			wantErr: false,
		},
		{
			name:   "success_with_empty_permissions",
			userID: "550e8400-e29b-41d4-a716-446655440001",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
					if perms, ok := dest.(*[]authorizer.Permission); ok {
						*perms = []authorizer.Permission{}
					}
					return nil
				},
			},
			expectedResponse: &UserPermissionsResponse{
				UserID:      "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []PermissionScope{},
			},
			wantErr: false,
		},
		{
			name:   "authorizer_error",
			userID: "550e8400-e29b-41d4-a716-446655440001",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
					return fmt.Errorf("database error")
				},
			},
			expectedResponse: nil,
			expectedError:    fmt.Errorf("database error"),
			wantErr:          true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Store original function
			originalGetUserPermissions := getUserPermissions
			defer func() {
				getUserPermissions = originalGetUserPermissions
			}()

			// Execute the ACTUAL function (not mocked)
			actualFunc := originalGetUserPermissions
			result, err := actualFunc(tt.mockDB, tt.userID)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
				if tt.expectedError != nil {
					assert.Contains(t, err.Error(), tt.expectedError.Error())
				}
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.expectedResponse.UserID, result.UserID)
				assert.Equal(t, len(tt.expectedResponse.Permissions), len(result.Permissions))

				// Compare permissions in detail
				for i, expectedPerm := range tt.expectedResponse.Permissions {
					assert.Equal(t, expectedPerm.Scope, result.Permissions[i].Scope)
					assert.Equal(t, expectedPerm.ScopeID, result.Permissions[i].ScopeID)
					assert.Equal(t, expectedPerm.OrganizationID, result.Permissions[i].OrganizationID)
					assert.Equal(t, expectedPerm.Permissions, result.Permissions[i].Permissions)
				}
			}
		})
	}
}

// Test_Handler tests the default Handler variable
func Test_Handler(t *testing.T) {
	t.Parallel()

	// Test that Handler is properly initialized
	assert.NotNil(t, Handler, "Handler should be initialized")

	// Test that it's a function
	assert.IsType(t, http.HandlerFunc(nil), Handler, "Handler should be an http.HandlerFunc")
}

// Helper function to create string pointers
func stringPtr(s string) *string {
	return &s
}
