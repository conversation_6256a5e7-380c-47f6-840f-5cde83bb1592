package permissions

import (
	"context"
	"net/http"

	"synapse-its.com/shared/api/authorizer"
	"synapse-its.com/shared/api/response"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
)

// HandlerDeps bundles dependencies for injection and testing.
type HandlerDeps struct {
	UserPermissionsFromContext func(ctx context.Context) (*authorizer.UserPermissions, bool)
	GetConnections             func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error)
	GetUserPermissions         func(pg connect.DatabaseExecutor, userID string) (*UserPermissionsResponse, error)
}

// HandlerWithDeps returns an http.HandlerFunc with injected deps.
func HandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get user info from jwt authorizer using dependency
		userPermissions, ok := deps.UserPermissionsFromContext(ctx)
		var userID string

		if !ok {
			// Fallback: Check for test-user-id header for integration testing
			testUserID := r.Header.Get("test-user-id")
			if testUserID != "" {
				userID = testUserID
				logger.Debugf("Using test-user-id header: %s", userID)
			} else {
				logger.Error(ErrUserInfoRetrieve.Error())
				response.CreateInternalErrorResponse(w)
				return
			}
		} else {
			userID = userPermissions.UserID
		}

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		// Get User Permissions using dependency
		permissions, err := deps.GetUserPermissions(pg, userID)
		if err != nil {
			logger.Errorf("Error getting user permissions: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		response.CreateSuccessResponse(permissions, w)
	}
}

// Handler is the production-ready HTTP handler using default dependencies.
var Handler = HandlerWithDeps(HandlerDeps{
	UserPermissionsFromContext: authorizer.UserPermissionsFromContext,
	GetConnections:             connect.GetConnections,
	GetUserPermissions:         getUserPermissions,
})

// getUserPermissions retrieves user permissions and transforms them to API format
var getUserPermissions = func(pg connect.DatabaseExecutor, userID string) (*UserPermissionsResponse, error) {
	// Use the existing authorizer function to get permissions
	permissions, err := authorizer.GetUserPermissions(pg, userID)
	if err != nil {
		return nil, err
	}

	// Transform to API response format
	response := &UserPermissionsResponse{
		UserID:      userID,
		Permissions: make([]PermissionScope, len(permissions.Permissions)),
	}

	for i, perm := range permissions.Permissions {
		var scopeID *string
		var orgID *string

		// Handle scope ID (null for global scope)
		if perm.ScopeID != "" {
			scopeID = &perm.ScopeID
		}

		// Handle organization ID (null for global scope)
		if perm.OrganizationID != "" {
			orgID = &perm.OrganizationID
		}

		response.Permissions[i] = PermissionScope{
			Scope:          perm.Scope,
			ScopeID:        scopeID,
			OrganizationID: orgID,
			Permissions:    perm.Permissions,
		}
	}

	return response, nil
}
