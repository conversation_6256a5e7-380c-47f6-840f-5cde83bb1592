package authorizer

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"net/http"
	"slices"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/lib/pq"
	"synapse-its.com/shared/api/jwttokens"
	"synapse-its.com/shared/api/security"
	connect "synapse-its.com/shared/connect"
)

// =============================================================================
// CONSTANTS AND TYPES
// =============================================================================

type ctxKey string

const (
	userPermissionsKey ctxKey = "userPermissions"
	headerJWT          string = "jwt-token"
)

// =============================================================================
// MAIN AUTHORIZATION MIDDLEWARE
// =============================================================================

// Authorize is the main authorization middleware that validates JWT tokens and checks user permissions
// It extracts the JWT token from the request header, validates it, retrieves user permissions,
// and checks if the user has access to the requested endpoint.
var Authorize = func(ctx context.Context, r *http.Request) (context.Context, error) {
	// Get the jwt-token
	authToken := r.Header.Get(headerJWT)
	if authToken == "" {
		return ctx, fmt.Errorf("%w: jwt-token not found in header", ErrUnauthorized)
	}

	// Validate the jwt-token in the header
	_, _, err := jwttokens.ValidateJJwtToken(authToken)
	if err != nil {
		return ctx, fmt.Errorf("%w: %v", ErrUnauthorized, err)
	}

	// Get the postgres connection.
	connections, err := connect.GetConnections(ctx)
	if err != nil {
		return ctx, fmt.Errorf("%w: %w", ErrInternal, err)
	}
	pg := connections.Postgres

	// Validate token and get user info
	userPermissions, err := getUserFromToken(pg, authToken)
	if err != nil {
		return ctx, err
	}

	// TODO: Make this even more configurable in the future
	if !validEndPointRequestFromRole(userPermissions, r) {
		return ctx, fmt.Errorf("%w: user does not have the correct permissions to access %v", ErrForbidden, r.URL.Path)
	}

	// allow access
	return context.WithValue(ctx, userPermissionsKey, userPermissions), nil
}

// UserPermissionsFromContext extracts user permissions from the request context
// Returns the UserPermissions and a boolean indicating if the extraction was successful
func UserPermissionsFromContext(ctx context.Context) (*UserPermissions, bool) {
	up, ok := ctx.Value(userPermissionsKey).(*UserPermissions)
	return up, ok
}

// AddUserPermissionsToContext adds user permissions to context for testing or custom scenarios
// This is useful when you need to manually set permissions without going through JWT validation
func AddUserPermissionsToContext(ctx context.Context, userPermissions UserPermissions) context.Context {
	return context.WithValue(ctx, userPermissionsKey, userPermissions)
}

// =============================================================================
// TOKEN VALIDATION AND USER RETRIEVAL
// =============================================================================

// getUserFromToken validates a JWT token against the database and retrieves user permissions
// It checks if the token exists, is not expired, and belongs to an enabled user
var getUserFromToken = func(pg connect.DatabaseExecutor, jwtToken string) (*UserPermissions, error) {
	// All DB jwts are stored as SHA256
	sha256TokenValue := security.CalculateSHA256(jwtToken)

	query := `
		SELECT 
			u.Id,
			ut.Expiration::timestamptz AS expirationutc 
		FROM {{User}} u
		JOIN {{UserToken}} ut
			ON u.Id = ut.UserId 
		WHERE NOT u.IsDeleted AND ut.JWTTokenSha256 = $1`
	userInfo := &dbUserInfo{}
	if err := pg.QueryRowStruct(userInfo, query, sha256TokenValue); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			// if no rows, return error
			return nil, fmt.Errorf("%w: token not found or the user is disabled", ErrUnauthorized)
		}
		// return general error
		return nil, fmt.Errorf("%w: %v", ErrInternal, err)
	}

	// check the token expiration
	if now := time.Now().UTC(); now.After(userInfo.ExpirationUTC) {
		return nil, fmt.Errorf("%w: token expired at %s", ErrForbidden, userInfo.ExpirationUTC)
	}

	permissions, err := GetUserPermissions(pg, userInfo.UserID)
	if err != nil {
		return nil, fmt.Errorf("%w: %v", ErrInternal, err)
	}

	return permissions, nil
}

// =============================================================================
// ENDPOINT PERMISSION VALIDATION
// =============================================================================

// validEndPointRequestFromRole validates whether the user has access to the requested endpoint
// It maps endpoints to required permissions and checks if the user has any of those permissions
var validEndPointRequestFromRole = func(userPermissions *UserPermissions, r *http.Request) bool {
	// Define endpoint-to-permission mappings
	endpointPermissions := map[string][]string{
		// Device endpoints - require device viewing permissions
		"/data/v2/device":     {"org_view_devices", "device_group_view_devices", "location_group_view_devices"},
		"/api/v3/data/device": {"org_view_devices", "device_group_view_devices", "location_group_view_devices"},

		// Fault endpoints - require device viewing permissions
		"/data/v2/fault":     {"org_view_devices", "device_group_view_devices", "location_group_view_devices"},
		"/api/v3/data/fault": {"org_view_devices", "device_group_view_devices", "location_group_view_devices"},

		// User account notifications - require device viewing permissions
		"/user/v2/account/notifications":     {"org_view_devices", "device_group_view_devices", "location_group_view_devices"},
		"/api/v3/user/account/notifications": {"org_view_devices", "device_group_view_devices", "location_group_view_devices"},

		// User instructions - device management permissions
		"/user/v3/instruction":     {"device_group_manage_devices", "location_group_manage_devices", "org_manage_devices"},
		"/api/v3/user/instruction": {"device_group_manage_devices", "location_group_manage_devices", "org_manage_devices"},
	}

	// Get required permissions for this endpoint
	var requiredPermissions []string
	for endpoint, perms := range endpointPermissions {
		if strings.HasPrefix(r.URL.Path, endpoint) {
			requiredPermissions = perms
			break
		}
	}

	// If no specific permissions required, allow access (for non-protected endpoints)
	if len(requiredPermissions) == 0 {
		return true
	}

	// Check if user has any of the required permissions
	return hasAnyPermission(userPermissions, requiredPermissions)
}

// hasAnyPermission checks if the user has any of the required permissions across all scopes
// This is a utility function used by the endpoint validation logic
func hasAnyPermission(userPermissions *UserPermissions, requiredPermissions []string) bool {
	// Create a set of all user permissions for efficient lookup
	userPerms := make(map[string]bool)
	for _, permission := range userPermissions.Permissions {
		for _, perm := range permission.Permissions {
			userPerms[perm] = true
		}
	}

	// Check if user has any of the required permissions
	for _, requiredPerm := range requiredPermissions {
		if userPerms[requiredPerm] {
			return true
		}
	}

	return false
}

// =============================================================================
// USER PERMISSIONS QUERY METHODS
// =============================================================================

// HasPermission checks if the user has a specific permission in any scope
// This is useful for checking if a user has a particular permission regardless of scope
func (up *UserPermissions) HasPermission(permission string) bool {
	for _, perm := range up.Permissions {
		for _, p := range perm.Permissions {
			if p == permission {
				return true
			}
		}
	}
	return false
}

// HasPermissionInScope checks if the user has a specific permission in a specific scope
// This is useful when you need to verify permissions for a particular organization or device group
func (up *UserPermissions) HasPermissionInScope(permission, scope, scopeID string) bool {
	for _, perm := range up.Permissions {
		if perm.Scope == scope && perm.ScopeID == scopeID {
			for _, p := range perm.Permissions {
				if p == permission {
					return true
				}
			}
		}
	}
	return false
}

// GetPermissionsForScope returns all permissions for a specific scope
// This is useful for getting all permissions a user has in a particular organization or device group
func (up *UserPermissions) GetPermissionsForScope(scope, scopeID string) []string {
	for _, perm := range up.Permissions {
		if perm.Scope == scope && perm.ScopeID == scopeID {
			return perm.Permissions
		}
	}
	return []string{}
}

// GetAccessibleOrganizations returns the list of organization IDs the user has access to
// This provides a quick way to see which organizations a user can access
func (up *UserPermissions) GetAccessibleOrganizations() []string {
	orgSet := make(map[string]bool)

	for _, permission := range up.Permissions {
		orgSet[permission.OrganizationID] = true
	}

	var organizations []string
	for orgID := range orgSet {
		organizations = append(organizations, orgID)
	}

	return organizations
}

// GetAccessibleDeviceGroups returns the list of device group IDs the user has access to within an organization
// This is useful for filtering device groups based on user permissions
func (up *UserPermissions) GetAccessibleDeviceGroups(organizationID string) []string {
	var deviceGroups []string

	for _, permission := range up.Permissions {
		if permission.OrganizationID == organizationID && permission.Scope == "device_group" {
			deviceGroups = append(deviceGroups, permission.ScopeID)
		}
	}

	return deviceGroups
}

// GetAccessibleLocationGroups returns location group IDs that the user has access to within a given organization
// This is useful for filtering location groups based on user permissions
func (up *UserPermissions) GetAccessibleLocationGroups(organizationID string) []string {
	var locationGroups []string

	for _, permission := range up.Permissions {
		if permission.OrganizationID == organizationID && permission.Scope == "location_group" {
			locationGroups = append(locationGroups, permission.ScopeID)
		}
	}

	return locationGroups
}

// =============================================================================
// DEVICE ACCESS RESOLUTION METHODS
// =============================================================================

// GetAuthorizedDevices returns a list of device IDs that the user has access to based on their permissions
// This function queries the database to resolve the actual devices based on the user's scope permissions
// It supports organization-level, device group-level, and location group-level permissions
func (up *UserPermissions) GetAuthorizedDevices(pg connect.DatabaseExecutor, requiredPermissions ...string) ([]string, error) {
	if len(requiredPermissions) == 0 {
		requiredPermissions = []string{"org_view_devices", "device_group_view_devices", "location_group_view_devices"}
	}

	// Check if user has any of the required permissions
	if !up.hasAnyPermissionFromList(requiredPermissions) {
		return []string{}, nil // No permissions, return empty list
	}

	// Get devices from all permission scopes in a single query
	deviceIds, err := up.getDevicesForPermissionScopes(pg, up.Permissions, requiredPermissions)
	if err != nil {
		return nil, fmt.Errorf("failed to get devices for permission scopes: %w", err)
	}

	// The new function already returns unique results due to DISTINCT in queries
	return deviceIds, nil
}

// GetAuthorizedDevicesByOrganization returns devices the user can access within a specific organization
// This is useful when needing to filter devices by organization while respecting user permissions
func (up *UserPermissions) GetAuthorizedDevicesByOrganization(pg connect.DatabaseExecutor, organizationID string, requiredPermissions ...string) ([]string, error) {
	if len(requiredPermissions) == 0 {
		requiredPermissions = []string{"org_view_devices", "device_group_view_devices", "location_group_view_devices"}
	}

	// Filter permissions to only those within the specified organization
	var orgPermissions []Permission
	for _, permission := range up.Permissions {
		if permission.OrganizationID == organizationID {
			orgPermissions = append(orgPermissions, permission)
		}
	}

	// Get devices from permissions within the specified organization in a single query
	deviceIds, err := up.getDevicesForPermissionScopes(pg, orgPermissions, requiredPermissions)
	if err != nil {
		return nil, fmt.Errorf("failed to get devices for permission scopes in org %s: %w", organizationID, err)
	}

	// The new function already returns unique results due to DISTINCT in queries
	return deviceIds, nil
}

// CanAccessDevice checks if the user can access a specific device with the given permissions
// This function retrieves device information and checks if any of the user's permissions grant access
func (up *UserPermissions) CanAccessDevice(pg connect.DatabaseExecutor, deviceID string, requiredPermissions ...string) (bool, error) {
	if len(requiredPermissions) == 0 {
		requiredPermissions = []string{"org_view_devices", "device_group_view_devices", "location_group_view_devices"}
	}

	// Get the device's organization and group memberships
	deviceInfo, err := getDeviceInfo(pg, deviceID)
	if err != nil {
		return false, fmt.Errorf("failed to get device info: %w", err)
	}

	// Check each permission scope
	for _, permission := range up.Permissions {
		// Skip if user doesn't have any required permissions in this scope
		if !up.hasPermissionsInScope(permission, requiredPermissions) {
			continue
		}

		switch permission.Scope {
		case "org":
			// Organization-level permissions grant access to all devices in the organization
			if permission.ScopeID == deviceInfo.OrganizationID {
				return true, nil
			}

		case "device_group":
			// Device group permissions grant access to devices in that specific group
			for _, groupID := range deviceInfo.DeviceGroupIDs {
				if permission.ScopeID == groupID {
					return true, nil
				}
			}

		case "location_group":
			// Location group permissions grant access to devices in that location group
			for _, locationGroupID := range deviceInfo.LocationGroupIDs {
				if permission.ScopeID == locationGroupID {
					return true, nil
				}
			}
		}
	}

	return false, nil
}

// CanAccessDeviceByOrigID checks if the user can access a specific device using its OrigID with the given permissions
//
// DEPRECATED: This function is deprecated and should not be used in new code.
// Use CanAccessDevice(deviceId string, ...) instead which uses the device's UUID.
// This function exists for backward compatibility with legacy code that uses OrigID.
//
// This function retrieves device information by OrigID and checks if any of the user's permissions grant access
func (up *UserPermissions) CanAccessDeviceByOrigID(pg connect.DatabaseExecutor, origID int64, requiredPermissions ...string) (string, error) {
	if pg == nil {
		return "", fmt.Errorf("pg is nil")
	}

	if len(requiredPermissions) == 0 {
		requiredPermissions = []string{"org_view_devices", "device_group_view_devices", "location_group_view_devices"}
	}

	// Get the device's organization and group memberships using OrigID
	deviceInfo, err := getDeviceInfoByOrigID(pg, origID)
	if err != nil {
		return "", fmt.Errorf("failed to get device info by origID: %w", err)
	}

	// Check each permission scope
	for _, permission := range up.Permissions {
		// Skip if user doesn't have any required permissions in this scope
		if !up.hasPermissionsInScope(permission, requiredPermissions) {
			continue
		}

		switch permission.Scope {
		case "org":
			// Organization-level permissions grant access to all devices in the organization
			if permission.ScopeID == deviceInfo.OrganizationID {
				return deviceInfo.DeviceID, nil
			}

		case "device_group":
			// Device group permissions grant access to devices in that specific group
			if slices.Contains(deviceInfo.DeviceGroupIDs, permission.ScopeID) {
				return deviceInfo.DeviceID, nil
			}

		case "location_group":
			// Location group permissions grant access to devices in that location group
			if slices.Contains(deviceInfo.LocationGroupIDs, permission.ScopeID) {
				return deviceInfo.DeviceID, nil
			}
		}
	}

	return "", nil
}

// =============================================================================
// INTERNAL PERMISSION HELPER METHODS
// =============================================================================

// hasAnyPermissionFromList checks if user has any permission from the provided list
// This is an internal helper method used by device access functions
func (up *UserPermissions) hasAnyPermissionFromList(requiredPermissions []string) bool {
	for _, permission := range up.Permissions {
		for _, userPerm := range permission.Permissions {
			for _, requiredPerm := range requiredPermissions {
				if userPerm == requiredPerm {
					return true
				}
			}
		}
	}
	return false
}

// hasPermissionsInScope checks if a permission scope contains any of the required permissions
// This is an internal helper method used by device access functions
func (up *UserPermissions) hasPermissionsInScope(permission Permission, requiredPermissions []string) bool {
	for _, userPerm := range permission.Permissions {
		for _, requiredPerm := range requiredPermissions {
			if userPerm == requiredPerm {
				return true
			}
		}
	}
	return false
}

// =============================================================================
// DATABASE QUERY METHODS
// =============================================================================

// getDevicesForPermissionScopes gets device IDs for multiple permission scopes in a single JOIN query
// This is the optimized method that handles all permission types (org, device_group, location_group) in one database call
// It builds a single query with LEFT JOINs and OR conditions to efficiently retrieve all accessible devices
func (up *UserPermissions) getDevicesForPermissionScopes(pg connect.DatabaseExecutor, permissions []Permission, requiredPermissions []string) ([]string, error) {
	if len(permissions) == 0 {
		return []string{}, nil
	}

	// Filter permissions that have the required permissions and group by scope
	var orgScopes []string
	var deviceGroupScopes []string
	var locationGroupScopes []string

	for _, permission := range permissions {
		if !up.hasPermissionsInScope(permission, requiredPermissions) {
			continue
		}

		switch permission.Scope {
		case "org":
			orgScopes = append(orgScopes, permission.ScopeID)
		case "device_group":
			deviceGroupScopes = append(deviceGroupScopes, permission.ScopeID)
		case "location_group":
			locationGroupScopes = append(locationGroupScopes, permission.ScopeID)
		}
	}

	// If no valid scopes, return empty
	if len(orgScopes) == 0 && len(deviceGroupScopes) == 0 && len(locationGroupScopes) == 0 {
		return []string{}, nil
	}

	// Build WHERE conditions for each scope type
	var whereConditions []string
	var args []interface{}
	argIndex := 1

	// Add organization-level condition
	if len(orgScopes) > 0 {
		placeholders := make([]string, len(orgScopes))
		for i, orgID := range orgScopes {
			placeholders[i] = fmt.Sprintf("$%d", argIndex)
			args = append(args, orgID)
			argIndex++
		}
		whereConditions = append(whereConditions, fmt.Sprintf("sg.OrganizationId IN (%s)", strings.Join(placeholders, ",")))
	}

	// Add device group-level condition
	if len(deviceGroupScopes) > 0 {
		placeholders := make([]string, len(deviceGroupScopes))
		for i, groupID := range deviceGroupScopes {
			placeholders[i] = fmt.Sprintf("$%d", argIndex)
			args = append(args, groupID)
			argIndex++
		}
		whereConditions = append(whereConditions, fmt.Sprintf("dgd.DeviceGroupId IN (%s)", strings.Join(placeholders, ",")))
	}

	if len(locationGroupScopes) > 0 {
		placeholders := make([]string, len(locationGroupScopes))
		for i, locationGroupId := range locationGroupScopes {
			placeholders[i] = fmt.Sprintf("$%d", argIndex)
			args = append(args, locationGroupId)
			argIndex++
		}
		whereConditions = append(whereConditions, fmt.Sprintf("lgl.LocationGroupId IN (%s)", strings.Join(placeholders, ",")))
	}

	// Build a query with LEFT JOINs to handle all permission types
	query := fmt.Sprintf(`
		SELECT DISTINCT d.Id::text as device_id
		FROM {{Device}} d
		JOIN {{SoftwareGateway}} sg ON d.SoftwareGatewayId = sg.Id
		LEFT JOIN {{DeviceGroupDevices}} dgd ON d.Id = dgd.DeviceID
		LEFT JOIN {{Location}} l ON d.LocationId = l.Id
		LEFT JOIN {{LocationGroupLocations}} lgl ON l.Id = lgl.LocationId
		WHERE NOT d.IsDeleted
		AND (%s)`,
		strings.Join(whereConditions, " OR "))

	var deviceIds []struct {
		DeviceID string `db:"device_id"`
	}
	err := pg.QueryGenericSlice(&deviceIds, query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to query devices for multiple permission scopes: %w", err)
	}

	var deviceIDs []string
	for _, device := range deviceIds {
		deviceIDs = append(deviceIDs, device.DeviceID)
	}

	return deviceIDs, nil
}

// GetEligibleUsersForDevice returns all eligible users for fault notifications for the given device
//
// This function finds all users who have the specified permissions to access the given device.
// It works by:
// 1. Finding all users who have the required permissions in scopes that grant access to the device
// 2. Filtering for users with SMS notifications enabled and valid mobile numbers
//
// The function supports organization-level, device group-level, and location group-level permissions.
// Users are eligible if they have any of the required permissions in any scope that grants access to the device.
func GetEligibleUsersForDevice(ctx context.Context, pg connect.DatabaseExecutor, deviceID string, requiredPermissions ...string) ([]User, error) {
	if len(requiredPermissions) == 0 {
		requiredPermissions = []string{"org_view_devices", "device_group_view_devices", "location_group_view_devices"}
	}

	// Build the query using UNION ALL to combine results from different permission scopes
	query := `
		SELECT DISTINCT u.Id, u.Mobile, u.IanaTimezone
		FROM (
			-- Organization-level permissions
			SELECT DISTINCT u.Id, u.Mobile, u.IanaTimezone
			FROM {{Device}} d
			JOIN {{SoftwareGateway}} sg ON d.SoftwareGatewayId = sg.Id
			JOIN {{Organization}} o ON sg.OrganizationId = o.Id
			JOIN {{Memberships}} m ON o.Id = m.OrganizationId AND NOT m.IsDeleted
			JOIN {{AuthMethod}} am ON m.AuthMethodId = am.Id
			JOIN {{User}} u ON am.UserId = u.Id AND NOT u.IsDeleted AND u.Mobile IS NOT NULL AND u.NotificationSmsEnabled = true
			JOIN {{OrgRoleAssignments}} ora ON m.Id = ora.MembershipId AND ora.IsDeleted = false
			JOIN {{CustomRole}} cr ON ora.RoleId = cr.Id AND NOT cr.IsDeleted
			JOIN {{TemplateRole}} tr ON cr.TemplateRoleIdentifier = tr.Identifier
			JOIN {{TemplateRolePermission}} trp ON tr.Identifier = trp.TemplateRoleIdentifier
			JOIN {{Permission}} p ON trp.PermissionIdentifier = p.Identifier
			LEFT JOIN {{CustomRolePermission}} crp ON cr.Id = crp.CustomRoleId AND p.Identifier = crp.PermissionIdentifier
			WHERE d.Id = $1
				AND NOT d.IsDeleted
				AND COALESCE(crp.Value, trp.DefaultValue) = true
				AND p.Identifier = ANY($2)
			
			UNION ALL
			
			-- Device group permissions
			SELECT DISTINCT u.Id, u.Mobile, u.IanaTimezone
			FROM {{Device}} d
			JOIN {{DeviceGroupDevices}} dgd ON d.Id = dgd.DeviceID
			JOIN {{DeviceGroups}} dg ON dgd.DeviceGroupId = dg.Id AND dg.IsDeleted = false
			JOIN {{DeviceGroupRoleAssignments}} dgra ON dg.Id = dgra.DeviceGroupId AND dgra.IsDeleted = false
			JOIN {{Memberships}} m ON dgra.MembershipId = m.Id AND NOT m.IsDeleted
			JOIN {{AuthMethod}} am ON m.AuthMethodId = am.Id
			JOIN {{User}} u ON am.UserId = u.Id AND NOT u.IsDeleted AND u.Mobile IS NOT NULL AND u.NotificationSmsEnabled = true
			JOIN {{CustomRole}} cr ON dgra.RoleId = cr.Id AND NOT cr.IsDeleted
			JOIN {{TemplateRole}} tr ON cr.TemplateRoleIdentifier = tr.Identifier
			JOIN {{TemplateRolePermission}} trp ON tr.Identifier = trp.TemplateRoleIdentifier
			JOIN {{Permission}} p ON trp.PermissionIdentifier = p.Identifier
			LEFT JOIN {{CustomRolePermission}} crp ON cr.Id = crp.CustomRoleId AND p.Identifier = crp.PermissionIdentifier
			WHERE d.Id = $1
				AND NOT d.IsDeleted
				AND COALESCE(crp.Value, trp.DefaultValue) = true
				AND p.Identifier = ANY($2)
			
			UNION ALL
			
			-- Location group permissions
			SELECT DISTINCT u.Id, u.Mobile, u.IanaTimezone
			FROM {{Device}} d
			JOIN {{Location}} l ON d.LocationId = l.Id
			JOIN {{LocationGroupLocations}} lgl ON l.Id = lgl.LocationId
			JOIN {{LocationGroups}} lg ON lgl.LocationGroupId = lg.Id AND lg.IsDeleted = false
			JOIN {{LocationGroupRoleAssignments}} lgra ON lg.Id = lgra.LocationGroupId AND lgra.IsDeleted = false
			JOIN {{Memberships}} m ON lgra.MembershipId = m.Id AND NOT m.IsDeleted
			JOIN {{AuthMethod}} am ON m.AuthMethodId = am.Id
			JOIN {{User}} u ON am.UserId = u.Id AND NOT u.IsDeleted AND u.Mobile IS NOT NULL AND u.NotificationSmsEnabled = true
			JOIN {{CustomRole}} cr ON lgra.RoleId = cr.Id AND NOT cr.IsDeleted
			JOIN {{TemplateRole}} tr ON cr.TemplateRoleIdentifier = tr.Identifier
			JOIN {{TemplateRolePermission}} trp ON tr.Identifier = trp.TemplateRoleIdentifier
			JOIN {{Permission}} p ON trp.PermissionIdentifier = p.Identifier
			LEFT JOIN {{CustomRolePermission}} crp ON cr.Id = crp.CustomRoleId AND p.Identifier = crp.PermissionIdentifier
			WHERE d.Id = $1
				AND NOT d.IsDeleted
				AND COALESCE(crp.Value, trp.DefaultValue) = true
				AND p.Identifier = ANY($2)
		) u`

	var users []User
	err := pg.QueryGenericSlice(&users, query, deviceID, pq.Array(requiredPermissions))
	if err != nil {
		return nil, fmt.Errorf("failed to query eligible users for device %s: %w", deviceID, err)
	}

	return users, nil
}

// getDeviceInfo retrieves device information needed for authorization
// This function gets the organization ID and group memberships for a specific device
func getDeviceInfo(pg connect.DatabaseExecutor, deviceID string) (*DeviceInfo, error) {
	query := `
		SELECT 
			d.Id::text as device_id,
			sg.OrganizationId::text as organization_id,
			COALESCE(array_agg(DISTINCT dgd.DeviceGroupId::text) FILTER (WHERE dgd.DeviceGroupId IS NOT NULL), '{}') as device_group_ids,
			COALESCE(array_agg(DISTINCT lgl.LocationGroupId::text) FILTER (WHERE lgl.LocationGroupId IS NOT NULL), '{}') as location_group_ids
		FROM {{Device}} d
		JOIN {{SoftwareGateway}} sg ON d.SoftwareGatewayId = sg.Id
		LEFT JOIN {{DeviceGroupDevices}} dgd ON d.Id = dgd.DeviceID
		LEFT JOIN {{Location}} l ON d.LocationId = l.Id
		LEFT JOIN {{LocationGroupLocations}} lgl ON l.Id = lgl.LocationId
		WHERE d.Id = $1 AND NOT d.IsDeleted
		GROUP BY d.Id, sg.OrganizationId`

	var deviceInfo DeviceInfo
	err := pg.QueryRowStruct(&deviceInfo, query, deviceID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, fmt.Errorf("device not found: %s", deviceID)
		}
		return nil, err
	}

	return &deviceInfo, nil
}

// getDeviceInfoByOrigID retrieves device information needed for authorization using OrigID
//
// DEPRECATED: This function is deprecated and should not be used in new code.
// Use getDeviceInfo(deviceId string) instead which uses the device's UUID.
// This function exists for backward compatibility with legacy code that uses OrigID.
//
// This function gets the organization ID and group memberships for a specific device by its OrigID
func getDeviceInfoByOrigID(pg connect.DatabaseExecutor, origID int64) (*DeviceInfo, error) {
	query := `
		SELECT 
			d.Id::text as device_id,
			sg.OrganizationId::text as organization_id,
			COALESCE(array_agg(DISTINCT dgd.DeviceGroupId::text) FILTER (WHERE dgd.DeviceGroupId IS NOT NULL), '{}') as device_group_ids,
			COALESCE(array_agg(DISTINCT lgl.LocationGroupId::text) FILTER (WHERE lgl.LocationGroupId IS NOT NULL), '{}') as location_group_ids
		FROM {{Device}} d
		JOIN {{SoftwareGateway}} sg ON d.SoftwareGatewayId = sg.Id
		LEFT JOIN {{DeviceGroupDevices}} dgd ON d.Id = dgd.DeviceID
		LEFT JOIN {{Location}} l ON d.LocationId = l.Id
		LEFT JOIN {{LocationGroupLocations}} lgl ON l.Id = lgl.LocationId
		WHERE d.OrigID = $1 AND NOT d.IsDeleted
		GROUP BY d.Id, sg.OrganizationId`

	var deviceInfo DeviceInfo
	err := pg.QueryRowStruct(&deviceInfo, query, origID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, fmt.Errorf("device not found with origID: %d", origID)
		}
		return nil, err
	}

	return &deviceInfo, nil
}

// =============================================================================
// USER PERMISSIONS RETRIEVAL
// =============================================================================

// GetUserPermissions retrieves all permissions for a user across organizations, device groups, and location groups
//
// This function takes a user ID (UUID as string) and returns a comprehensive view of all permissions
// the user has across different scopes:
//
// - Organization-level permissions: Permissions that apply across the entire organization
// - Device group permissions: Permissions that apply to specific device groups within organizations
// - Location group permissions: Permissions that apply to specific location groups (not yet implemented)
//
// The function queries the database to find:
// 1. All organizations the user is a member of
// 2. All roles assigned to the user in each organization
// 3. All device groups the user has access to and their specific permissions
// 4. All location groups the user has access to (when implemented)
//
// Parameters:
//   - pg: Database executor for running queries
//   - userID: UUID string of the user to get permissions for
//
// Returns:
//   - *UserPermissions: Structured permissions data organized by scope
//   - error: Any error encountered during database operations or invalid UUID format
//
// Example usage:
//
//	permissions, err := GetUserPermissions(pg, "550e8400-e29b-41d4-a716-************")
//	if err != nil {
//	    log.Printf("Failed to get user permissions: %v", err)
//	    return
//	}
//
//	// Check organization permissions
//	for _, org := range permissions.Organizations {
//	    fmt.Printf("Organization: %s\n", org.OrganizationName)
//	    for _, perm := range org.Permissions {
//	        fmt.Printf("  %s: %t\n", perm.Name, perm.Value)
//	    }
//	}
//
//	// Check device group permissions
//	for _, dg := range permissions.DeviceGroups {
//	    fmt.Printf("Device Group: %s (Org: %s)\n", dg.DeviceGroupName, dg.OrganizationName)
//	    for _, perm := range dg.Permissions {
//	        fmt.Printf("  %s: %t\n", perm.Name, perm.Value)
//	    }
//	}
func GetUserPermissions(pg connect.DatabaseExecutor, userID string) (*UserPermissions, error) {
	// Validate userID is a valid UUID format
	if userID != "" {
		if _, err := uuid.Parse(userID); err != nil {
			return nil, fmt.Errorf("invalid user ID format: %w", err)
		}
	}

	permissions := &UserPermissions{
		UserID:      userID,
		Permissions: []Permission{},
	}

	// Return empty permissions for empty userID (no error, just no results)
	if userID == "" {
		return permissions, nil
	}

	// Get all permissions in a single query for efficiency
	allPerms, err := getAllUserPermissions(pg, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user permissions: %w", err)
	}

	// Ensure we always have a valid slice, even if empty
	if allPerms == nil {
		allPerms = []Permission{}
	}
	permissions.Permissions = allPerms

	return permissions, nil
}

// getAllUserPermissions retrieves all organization and device group permissions
// This is an internal function that performs the complex query to get all user permissions
// across different scopes and role assignments
func getAllUserPermissions(pg connect.DatabaseExecutor, userID string) ([]Permission, error) {
	query := `
		SELECT DISTINCT
			pg.Scope as scope,
			CASE 
				WHEN ora.MembershipId IS NOT NULL THEN o.Id::text
				WHEN dgra.MembershipId IS NOT NULL THEN dg.Id::text
				WHEN lgra.MembershipId IS NOT NULL THEN lg.Id::text
			END as scope_id,
			o.Id::text as organization_id,
			array_agg(DISTINCT p.Identifier ORDER BY p.Identifier) as permissions
		FROM {{User}} u
		JOIN {{AuthMethod}} am ON u.Id = am.UserId
		JOIN {{Memberships}} m ON am.Id = m.AuthMethodId
		JOIN {{Organization}} o ON m.OrganizationId = o.Id
		
		-- Left join all possible role assignment types
		LEFT JOIN {{OrgRoleAssignments}} ora ON m.Id = ora.MembershipId AND ora.IsDeleted = false
		LEFT JOIN {{DeviceGroupRoleAssignments}} dgra ON m.Id = dgra.MembershipId AND dgra.IsDeleted = false
		LEFT JOIN {{DeviceGroups}} dg ON dgra.DeviceGroupId = dg.Id AND dg.IsDeleted = false
		LEFT JOIN {{LocationGroupRoleAssignments}} lgra ON m.Id = lgra.MembershipId AND lgra.IsDeleted = false
		LEFT JOIN {{LocationGroups}} lg ON lgra.LocationGroupId = lg.Id AND lg.IsDeleted = false
		
		-- Get the role and permissions
		JOIN {{CustomRole}} cr ON (ora.RoleId = cr.Id OR dgra.RoleId = cr.Id OR lgra.RoleId = cr.Id)
		JOIN {{TemplateRole}} tr ON cr.TemplateRoleIdentifier = tr.Identifier
		JOIN {{TemplateRolePermission}} trp ON tr.Identifier = trp.TemplateRoleIdentifier
		JOIN {{Permission}} p ON trp.PermissionIdentifier = p.Identifier
		JOIN {{PermissionGroup}} pg ON p.PermissionGroupIdentifier = pg.Identifier
		LEFT JOIN {{CustomRolePermission}} crp ON cr.Id = crp.CustomRoleId AND p.Identifier = crp.PermissionIdentifier
		
		WHERE u.Id = $1
			AND NOT u.IsDeleted 
			AND NOT m.IsDeleted 
			AND NOT cr.IsDeleted
			AND COALESCE(crp.Value, trp.DefaultValue) = true -- Resolve the permission value
			-- Ensure there is at least one role assignment
			AND (ora.MembershipId IS NOT NULL OR dgra.MembershipId IS NOT NULL OR lgra.MembershipId IS NOT NULL)
		
		GROUP BY scope, scope_id, organization_id
		ORDER BY scope, scope_id, organization_id`

	var result []Permission
	err := pg.QueryGenericSlice(&result, query, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to query user permissions: %w", err)
	}

	return result, nil
}
